import React from 'react';
import { X, Mail, Phone, Linkedin, Award, Calendar, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface TeamMember {
  id: number;
  name: string;
  position: string;
  image: string;
  bio: string;
  specializations: string[];
  education: string[];
  experience: string;
  email: string;
  phone: string;
  linkedin?: string;
  achievements: string[];
  location: string;
}

interface TeamMemberModalProps {
  member: TeamMember | null;
  isOpen: boolean;
  onClose: () => void;
}

const TeamMemberModal: React.FC<TeamMemberModalProps> = ({ member, isOpen, onClose }) => {
  if (!isOpen || !member) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 p-2 bg-white/90 hover:bg-white rounded-full shadow-lg transition-all duration-200"
        >
          <X className="w-5 h-5 text-slate-600" />
        </button>

        {/* Header Section */}
        <div className="bg-gradient-to-br from-pentagon-blue via-slate-800 to-pentagon-blue text-white p-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            {/* Profile Image */}
            <div className="lg:col-span-1">
              <div className="relative w-48 h-48 mx-auto lg:mx-0">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-full object-cover rounded-full border-4 border-white/20"
                />
              </div>
            </div>

            {/* Basic Info */}
            <div className="lg:col-span-2 text-center lg:text-left">
              <h2 className="text-3xl lg:text-4xl font-bold mb-2">{member.name}</h2>
              <p className="text-xl text-pale-grey mb-4">{member.position}</p>
              
              <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <span className="text-sm">{member.location}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span className="text-sm">{member.experience}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Biography */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 mb-4">About</h3>
                  <p className="text-slate-600 leading-relaxed">{member.bio}</p>
                </CardContent>
              </Card>

              {/* Specializations */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Areas of Expertise</h3>
                  <div className="grid grid-cols-1 gap-3">
                    {member.specializations.map((spec, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-pentagon-blue rounded-full flex-shrink-0"></div>
                        <span className="text-slate-600">{spec}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Contact Information</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Mail className="w-5 h-5 text-pentagon-blue flex-shrink-0" />
                      <a 
                        href={`mailto:${member.email}`} 
                        className="text-slate-600 hover:text-pentagon-blue transition-colors"
                      >
                        {member.email}
                      </a>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="w-5 h-5 text-pentagon-blue flex-shrink-0" />
                      <a 
                        href={`tel:${member.phone}`} 
                        className="text-slate-600 hover:text-pentagon-blue transition-colors"
                      >
                        {member.phone}
                      </a>
                    </div>
                    {member.linkedin && (
                      <div className="flex items-center space-x-3">
                        <Linkedin className="w-5 h-5 text-pentagon-blue flex-shrink-0" />
                        <a 
                          href={member.linkedin} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-slate-600 hover:text-pentagon-blue transition-colors"
                        >
                          LinkedIn Profile
                        </a>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Education */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Education</h3>
                  <div className="space-y-3">
                    {member.education.map((edu, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <Award className="w-5 h-5 text-pentagon-blue flex-shrink-0 mt-0.5" />
                        <span className="text-slate-600">{edu}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Achievements */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 mb-4">Key Achievements</h3>
                  <div className="space-y-3">
                    {member.achievements.map((achievement, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-amber-500 rounded-full flex-shrink-0 mt-2"></div>
                        <span className="text-slate-600">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover text-white">
              <a href={`mailto:${member.email}`}>
                <Mail className="w-4 h-4 mr-2" />
                Send Email
              </a>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-pentagon-blue text-pentagon-blue hover:bg-pentagon-blue hover:text-white">
              <a href={`tel:${member.phone}`}>
                <Phone className="w-4 h-4 mr-2" />
                Call Now
              </a>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamMemberModal;
