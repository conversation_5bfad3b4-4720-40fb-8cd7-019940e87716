#!/usr/bin/env node

/**
 * Image Optimization Script for Pentagon Advocates Website
 * 
 * This script helps optimize images for better web performance:
 * 1. Identifies large images that need compression
 * 2. Provides recommendations for optimization
 * 3. Can be extended to automatically compress images
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PUBLIC_DIR = path.join(__dirname, '../public');
const MAX_SIZE_KB = 100; // 100KB threshold
const CRITICAL_SIZE_KB = 500; // 500KB critical threshold

function getFileSizeInKB(filePath) {
  const stats = fs.statSync(filePath);
  return Math.round(stats.size / 1024);
}

function analyzeImages() {
  console.log('🔍 Analyzing images in public directory...\n');
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
  const files = fs.readdirSync(PUBLIC_DIR);
  
  const imageFiles = files.filter(file => 
    imageExtensions.includes(path.extname(file).toLowerCase())
  );

  const results = {
    total: imageFiles.length,
    totalSize: 0,
    large: [],
    critical: [],
    optimized: []
  };

  imageFiles.forEach(file => {
    const filePath = path.join(PUBLIC_DIR, file);
    const sizeKB = getFileSizeInKB(filePath);
    results.totalSize += sizeKB;

    const fileInfo = {
      name: file,
      size: sizeKB,
      path: filePath
    };

    if (sizeKB > CRITICAL_SIZE_KB) {
      results.critical.push(fileInfo);
    } else if (sizeKB > MAX_SIZE_KB) {
      results.large.push(fileInfo);
    } else {
      results.optimized.push(fileInfo);
    }
  });

  return results;
}

function printResults(results) {
  console.log(`📊 Image Analysis Results`);
  console.log(`========================`);
  console.log(`Total images: ${results.total}`);
  console.log(`Total size: ${results.totalSize} KB (${(results.totalSize / 1024).toFixed(2)} MB)\n`);

  if (results.critical.length > 0) {
    console.log(`🚨 CRITICAL - Images over ${CRITICAL_SIZE_KB}KB:`);
    results.critical.forEach(img => {
      console.log(`   ${img.name}: ${img.size} KB`);
    });
    console.log();
  }

  if (results.large.length > 0) {
    console.log(`⚠️  LARGE - Images over ${MAX_SIZE_KB}KB:`);
    results.large.forEach(img => {
      console.log(`   ${img.name}: ${img.size} KB`);
    });
    console.log();
  }

  if (results.optimized.length > 0) {
    console.log(`✅ OPTIMIZED - Images under ${MAX_SIZE_KB}KB:`);
    results.optimized.forEach(img => {
      console.log(`   ${img.name}: ${img.size} KB`);
    });
    console.log();
  }
}

function printRecommendations(results) {
  console.log(`💡 Optimization Recommendations`);
  console.log(`===============================`);

  if (results.critical.length > 0) {
    console.log(`\n🔥 IMMEDIATE ACTION REQUIRED:`);
    results.critical.forEach(img => {
      const potentialSavings = img.size - MAX_SIZE_KB;
      console.log(`   ${img.name}:`);
      console.log(`     Current: ${img.size} KB`);
      console.log(`     Target: ~${MAX_SIZE_KB} KB`);
      console.log(`     Potential savings: ~${potentialSavings} KB`);
      console.log(`     Tools: TinyPNG, ImageOptim, or online compressors`);
      console.log();
    });
  }

  if (results.large.length > 0) {
    console.log(`\n📈 RECOMMENDED OPTIMIZATIONS:`);
    results.large.forEach(img => {
      const potentialSavings = Math.round(img.size * 0.3); // Assume 30% compression
      console.log(`   ${img.name}: ${img.size} KB → ~${img.size - potentialSavings} KB`);
    });
    console.log();
  }

  const totalPotentialSavings = results.critical.reduce((sum, img) => sum + (img.size - MAX_SIZE_KB), 0) +
                               results.large.reduce((sum, img) => sum * 0.3, 0);

  if (totalPotentialSavings > 0) {
    console.log(`💾 Total potential savings: ~${Math.round(totalPotentialSavings)} KB`);
    console.log(`📉 Estimated load time improvement: ${Math.round(totalPotentialSavings / 100)}% faster`);
  }

  console.log(`\n🛠️  Optimization Tools:`);
  console.log(`   • Online: TinyPNG.com, Squoosh.app`);
  console.log(`   • Desktop: ImageOptim (Mac), TinyPNG Desktop`);
  console.log(`   • CLI: imagemin, sharp, squoosh-cli`);
  console.log(`   • Convert to WebP for modern browsers`);
}

// Run the analysis
const results = analyzeImages();
printResults(results);
printRecommendations(results);

export { analyzeImages, printResults, printRecommendations };
