/**
 * Content Security Utilities for Pentagon Advocates Website
 * 
 * These utilities provide additional layers of content protection
 * beyond the React hooks and components.
 */

// Disable common keyboard shortcuts
export const disableKeyboardShortcuts = () => {
  document.addEventListener('keydown', (e) => {
    // Disable F12 (Developer Tools)
    if (e.key === 'F12') {
      e.preventDefault();
      return false;
    }

    // Disable Ctrl+Shift+I (Developer Tools)
    if (e.ctrlKey && e.shiftKey && e.key === 'I') {
      e.preventDefault();
      return false;
    }

    // Disable Ctrl+Shift+J (Console)
    if (e.ctrlKey && e.shiftKey && e.key === 'J') {
      e.preventDefault();
      return false;
    }

    // Disable Ctrl+U (View Source)
    if (e.ctrlKey && e.key === 'u') {
      e.preventDefault();
      return false;
    }

    // Disable Ctrl+Shift+C (Inspect Element)
    if (e.ctrl<PERSON><PERSON> && e.shiftKey && e.key === 'C') {
      e.preventDefault();
      return false;
    }

    // Disable Ctrl+A (Select All)
    if (e.ctrlKey && e.key === 'a') {
      e.preventDefault();
      return false;
    }

    // Disable Ctrl+S (Save Page)
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault();
      return false;
    }

    // Disable Ctrl+P (Print)
    if (e.ctrlKey && e.key === 'p') {
      e.preventDefault();
      return false;
    }

    // Disable Ctrl+C (Copy)
    if (e.ctrlKey && e.key === 'c') {
      e.preventDefault();
      return false;
    }
  });
};

// Disable right-click context menu
export const disableRightClick = () => {
  document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
    alert('Right-click is disabled to protect content.');
    return false;
  });
};

// Disable text selection
export const disableTextSelection = () => {
  document.addEventListener('selectstart', (e) => {
    e.preventDefault();
    return false;
  });

  document.addEventListener('mousedown', (e) => {
    if (e.detail > 1) {
      e.preventDefault();
      return false;
    }
  });
};

// Disable drag and drop
export const disableDragAndDrop = () => {
  document.addEventListener('dragstart', (e) => {
    e.preventDefault();
    return false;
  });
};

// Basic developer tools detection
export const detectDevTools = () => {
  let devtools = { open: false };
  
  const threshold = 160;
  
  setInterval(() => {
    if (
      window.outerHeight - window.innerHeight > threshold ||
      window.outerWidth - window.innerWidth > threshold
    ) {
      if (!devtools.open) {
        devtools.open = true;
        alert('Developer tools detected. Please close them to continue.');
        // Optionally redirect or take other action
        // window.location.href = '/';
      }
    } else {
      devtools.open = false;
    }
  }, 500);
};

// Disable image saving
export const disableImageSaving = () => {
  document.addEventListener('DOMContentLoaded', () => {
    const images = document.querySelectorAll('img');
    images.forEach((img) => {
      img.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        alert('Image saving is disabled.');
        return false;
      });
      
      img.addEventListener('dragstart', (e) => {
        e.preventDefault();
        return false;
      });
      
      // Make images non-draggable
      img.draggable = false;
    });
  });
};

// Console warning message
export const showConsoleWarning = () => {
  console.clear();
  console.log(
    '%c⚠️ WARNING ⚠️',
    'color: red; font-size: 30px; font-weight: bold;'
  );
  console.log(
    '%cThis is a browser feature intended for developers. Content on this website is protected by copyright law. Unauthorized access, copying, or distribution is prohibited.',
    'color: red; font-size: 16px;'
  );
  console.log(
    '%cPentagon Advocates - Legal Services',
    'color: #18213E; font-size: 14px; font-weight: bold;'
  );
};

// Initialize all protection measures
export const initializeContentProtection = () => {
  disableKeyboardShortcuts();
  disableRightClick();
  disableTextSelection();
  disableDragAndDrop();
  disableImageSaving();
  detectDevTools();
  showConsoleWarning();
  
  // Add protection classes to body
  document.body.classList.add('content-protected', 'no-select');
  
  // Disable print for sensitive content
  const style = document.createElement('style');
  style.textContent = `
    @media print {
      .no-print, .protected-content {
        display: none !important;
      }
    }
  `;
  document.head.appendChild(style);
};

// Clean up protection (for development)
export const removeContentProtection = () => {
  document.body.classList.remove('content-protected', 'no-select');
  // Note: Event listeners would need to be tracked to remove them properly
  console.log('Content protection removed (development mode)');
};
