import { useEffect } from 'react';
import { getProtectionConfig, type ContentProtectionConfig } from '@/config/contentProtection';

interface ContentProtectionOptions extends Partial<ContentProtectionConfig> {
  // Allow overriding any config option
}

const useContentProtection = (options: ContentProtectionOptions = {}) => {
  const config = getProtectionConfig();
  const settings = { ...config, ...options };

  const {
    enabled,
    disableRightClick,
    disableTextSelection,
    disableKeyboardShortcuts,
    disableDragAndDrop,
    disableDevTools,
    showWarningMessages,
    warningMessages
  } = settings;

  useEffect(() => {
    // Skip if protection is disabled
    if (!enabled) return;

    const showWarning = (type: keyof typeof warningMessages = 'general') => {
      if (showWarningMessages) {
        alert(warningMessages[type]);
      }
    };

    // Disable right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      if (disableRightClick) {
        e.preventDefault();
        showWarning('rightClick');
        return false;
      }
    };

    // Disable text selection
    const handleSelectStart = (e: Event) => {
      if (disableTextSelection) {
        e.preventDefault();
        showWarning('textSelection');
        return false;
      }
    };

    // Disable drag and drop
    const handleDragStart = (e: DragEvent) => {
      if (disableDragAndDrop) {
        e.preventDefault();
        showWarning('general');
        return false;
      }
    };

    // Disable keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disableKeyboardShortcuts) {
        // Disable common copy/paste shortcuts
        if (
          (e.ctrlKey && (e.key === 'c' || e.key === 'C')) || // Ctrl+C
          (e.ctrlKey && (e.key === 'a' || e.key === 'A')) || // Ctrl+A
          (e.ctrlKey && (e.key === 's' || e.key === 'S')) || // Ctrl+S
          (e.ctrlKey && (e.key === 'p' || e.key === 'P')) || // Ctrl+P
          (e.ctrlKey && (e.key === 'x' || e.key === 'X')) || // Ctrl+X
          (e.ctrlKey && (e.key === 'v' || e.key === 'V')) || // Ctrl+V
          (e.ctrlKey && e.shiftKey && (e.key === 'i' || e.key === 'I')) || // Ctrl+Shift+I
          (e.ctrlKey && e.shiftKey && (e.key === 'j' || e.key === 'J')) || // Ctrl+Shift+J
          (e.ctrlKey && e.shiftKey && (e.key === 'c' || e.key === 'C')) || // Ctrl+Shift+C
          (e.ctrlKey && (e.key === 'u' || e.key === 'U')) || // Ctrl+U
          e.key === 'F12' // F12
        ) {
          e.preventDefault();
          showWarning('keyboardShortcuts');
          return false;
        }
      }
    };

    // Disable developer tools detection (basic)
    const handleDevTools = () => {
      if (disableDevTools) {
        const devtools = {
          open: false,
          orientation: null as string | null
        };
        
        const threshold = 160;
        
        setInterval(() => {
          if (
            window.outerHeight - window.innerHeight > threshold ||
            window.outerWidth - window.innerWidth > threshold
          ) {
            if (!devtools.open) {
              devtools.open = true;
              showWarning('devTools');
              // Optionally redirect or take other action
              // window.location.href = 'about:blank';
            }
          } else {
            devtools.open = false;
          }
        }, 500);
      }
    };

    // Add event listeners
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('selectstart', handleSelectStart);
    document.addEventListener('dragstart', handleDragStart);
    document.addEventListener('keydown', handleKeyDown);

    // Initialize dev tools detection
    if (disableDevTools) {
      handleDevTools();
    }

    // Add CSS to disable text selection
    if (disableTextSelection) {
      const style = document.createElement('style');
      style.textContent = `
        * {
          -webkit-user-select: none !important;
          -moz-user-select: none !important;
          -ms-user-select: none !important;
          user-select: none !important;
          -webkit-touch-callout: none !important;
          -webkit-tap-highlight-color: transparent !important;
        }
        
        /* Allow selection for input fields */
        input, textarea, [contenteditable="true"] {
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
          user-select: text !important;
        }
      `;
      document.head.appendChild(style);

      // Cleanup function to remove style
      return () => {
        document.head.removeChild(style);
      };
    }

    // Cleanup event listeners
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('selectstart', handleSelectStart);
      document.removeEventListener('dragstart', handleDragStart);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    enabled,
    disableRightClick,
    disableTextSelection,
    disableKeyboardShortcuts,
    disableDragAndDrop,
    disableDevTools,
    showWarningMessages,
    warningMessages
  ]);
};

export default useContentProtection;
