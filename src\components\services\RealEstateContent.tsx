import React from 'react';
import { Link } from 'react-router-dom';
import { Building2, CheckCircle, ArrowRight, FileText, Users, Shield, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Section, SectionHeader } from '@/components/ui/section';

export const RealEstateContent = () => {
  const services = [
    {
      title: "Property Conveyancing",
      description: "Complete conveyancing services for buying, selling, and transferring property ownership."
    },
    {
      title: "Real Estate Transactions",
      description: "Legal support for all types of real estate transactions, from residential to commercial."
    },
    {
      title: "Investment Structuring",
      description: "Strategic legal advice for real estate investments and portfolio development."
    },
    {
      title: "Due Diligence Services",
      description: "Comprehensive property investigations and risk assessment for informed decisions."
    },
    {
      title: "Lease Agreements",
      description: "Drafting and reviewing commercial and residential lease agreements."
    },
    {
      title: "Property Development",
      description: "Legal guidance for property development projects from planning to completion."
    }
  ];

  const process = [
    {
      step: "1",
      title: "Initial Assessment",
      description: "We evaluate your real estate needs and provide preliminary legal guidance."
    },
    {
      step: "2",
      title: "Due Diligence",
      description: "Comprehensive property investigation and legal risk assessment."
    },
    {
      step: "3",
      title: "Documentation",
      description: "Preparation and review of all necessary legal documents and contracts."
    },
    {
      step: "4",
      title: "Transaction Completion",
      description: "Overseeing the completion of your real estate transaction."
    }
  ];

  const benefits = [
    {
      icon: Shield,
      title: "Transaction Security",
      description: "Ensure your real estate transactions are legally protected and properly executed."
    },
    {
      icon: Users,
      title: "Expert Guidance",
      description: "Benefit from our extensive experience in Ugandan real estate law and practices."
    },
    {
      icon: Clock,
      title: "Efficient Processing",
      description: "Streamlined handling of real estate matters to minimize delays and complications."
    },
    {
      icon: FileText,
      title: "Complete Documentation",
      description: "Thorough preparation of all necessary legal documents and filings."
    }
  ];

  return (
    <>
      {/* Services Overview */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Our Real Estate Services"
          title="Comprehensive Property Legal Solutions"
          description="We provide end-to-end legal services for all your real estate needs, ensuring smooth and secure property transactions."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-pentagon-blue rounded-lg flex items-center justify-center mb-4">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-slate-900 mb-3">{service.title}</h3>
                <p className="text-slate-600 leading-relaxed">{service.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Process Section */}
      <Section className="bg-slate-50">
        <SectionHeader
          subtitle="Our Process"
          title="How We Handle Real Estate Matters"
          description="Our structured approach ensures thorough handling of your real estate needs from initial assessment to transaction completion."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {process.map((item, index) => (
            <Card key={index} className="text-center border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="w-16 h-16 bg-pentagon-blue text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h3 className="text-lg font-bold text-slate-900 mb-3">{item.title}</h3>
                <p className="text-slate-600 leading-relaxed">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Benefits Section */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Why Choose Us"
          title="Benefits of Our Real Estate Services"
          description="Pentagon Advocates brings extensive experience and local expertise to every real estate matter."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => {
            const IconComponent = benefit.icon;
            return (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-pentagon-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-slate-900 mb-3">{benefit.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{benefit.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* Expertise Section */}
      <Section className="bg-slate-50">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <SectionHeader
              subtitle="Our Expertise"
              title="Deep Understanding of Real Estate Law"
              description="Our team has extensive experience in all aspects of real estate law, from residential transactions to commercial developments."
              centered={false}
            />
            
            <div className="space-y-4 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Residential Transactions</h4>
                  <p className="text-slate-600 text-sm">Expert handling of home purchases, sales, and transfers.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Commercial Real Estate</h4>
                  <p className="text-slate-600 text-sm">Comprehensive support for commercial property transactions and leasing.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Investment Properties</h4>
                  <p className="text-slate-600 text-sm">Strategic legal advice for real estate investment portfolios.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Development Projects</h4>
                  <p className="text-slate-600 text-sm">Legal guidance for property development from planning to completion.</p>
                </div>
              </div>
            </div>
            
            <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue">
              <Link to="/contact">Discuss Your Real Estate Needs</Link>
            </Button>
          </div>
          
          <div>
            <Card className="bg-white border-0 shadow-xl">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-slate-900 mb-6">Common Real Estate Issues We Handle</h3>
                <div className="space-y-4">
                  {[
                    "Property purchase and sale agreements",
                    "Title searches and verification",
                    "Mortgage and financing documentation",
                    "Lease agreement drafting and review",
                    "Property development approvals",
                    "Zoning and land use compliance",
                    "Real estate investment structuring",
                    "Commercial property transactions"
                  ].map((issue, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <ArrowRight className="w-4 h-4 text-pentagon-blue flex-shrink-0" />
                      <span className="text-slate-700">{issue}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>
    </>
  );
};
