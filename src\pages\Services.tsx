import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Building2, Scale, Award, Heart, ArrowRight, CheckCircle, Users, Clock, Shield, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Section, SectionHeader } from '@/components/ui/section';
import { ServiceModal } from '@/components/ui/service-modal';
import { LandLawContent } from '@/components/services/LandLawContent';
import { RealEstateContent } from '@/components/services/RealEstateContent';
import { EstatePlanningContent } from '@/components/services/EstatePlanningContent';
import { FamilyLawContent } from '@/components/services/FamilyLawContent';

const Services = () => {
  const [activeModal, setActiveModal] = useState<string | null>(null);

  const services = [
    {
      icon: Scale,
      title: "Land Law & Litigation",
      description: "Expert representation in land disputes, property rights, and ownership matters across Uganda.",
      features: [
        "Land dispute resolution",
        "Property rights protection",
        "Boundary disputes",
        "Title verification",
        "Land registration",
        "Customary land matters"
      ],
      href: "/services/land-law",
      color: "from-amber-500 to-amber-600"
    },
    {
      icon: Building2,
      title: "Real Estate Law",
      description: "Comprehensive legal support for property transactions, conveyancing, and real estate investments.",
      features: [
        "Property conveyancing",
        "Real estate transactions",
        "Investment structuring",
        "Due diligence",
        "Lease agreements",
        "Property development"
      ],
      href: "/services/real-estate",
      color: "from-green-500 to-green-600"
    },
    {
      icon: Award,
      title: "Estate Planning",
      description: "Strategic planning for asset distribution, wills, trusts, and inheritance matters.",
      features: [
        "Will drafting",
        "Trust establishment",
        "Succession planning",
        "Probate administration",
        "Asset protection",
        "Tax planning"
      ],
      href: "/services/estate-planning",
      color: "from-pentagon-blue to-pentagon-blue"
    },
    {
      icon: Heart,
      title: "Family Law",
      description: "Compassionate legal support for marriage, divorce, child custody, and domestic relations.",
      features: [
        "Divorce proceedings",
        "Child custody",
        "Marriage contracts",
        "Spousal support",
        "Adoption services",
        "Domestic violence protection"
      ],
      href: "/services/family-law",
      color: "from-pink-500 to-pink-600"
    }
  ];

  const whyChooseUs = [
    {
      icon: Users,
      title: "Expert Team",
      description: "Experienced legal professionals with deep expertise in Ugandan law."
    },
    {
      icon: Clock,
      title: "Timely Service",
      description: "Efficient handling of legal matters with respect for your time."
    },
    {
      icon: Shield,
      title: "Trusted Advice",
      description: "Reliable legal counsel you can depend on for important decisions."
    }
  ];

  return (
    <>
      {/* Gradient Header */}
      <div className="bg-gradient-to-br from-pentagon-blue via-slate-800 to-pentagon-blue text-white py-12 px-6 lg:px-12">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Left: Breadcrumbs */}
            <div className="order-2 lg:order-1">
              <nav className="flex items-center space-x-2 text-sm text-slate-300 mb-4">
                <Home className="w-4 h-4" />
                <ChevronRight className="w-4 h-4" />
                <span className="text-white font-medium">Services</span>
              </nav>
              <p className="text-slate-300 text-lg leading-relaxed">
                Pentagon Advocates offers expert legal services across our core practice areas. From land law to family matters, we provide the expertise and dedication you need to achieve your legal objectives.
              </p>
            </div>

            {/* Right: Page Title */}
            <div className="order-1 lg:order-2 text-left lg:text-right">
              <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight">
                Comprehensive Legal Solutions
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Services Overview */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Our Practice Areas"
          title="Legal Services We Provide"
          description="We specialize in four core areas of law, providing comprehensive legal solutions tailored to meet your unique needs."
        />
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-pentagon-blue rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-slate-900 mb-4">
                    {service.title}
                  </h3>
                  
                  <p className="text-slate-600 mb-6 leading-relaxed">
                    {service.description}
                  </p>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-slate-900 mb-3">Key Services:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                          <span className="text-sm text-slate-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <button
                    onClick={() => setActiveModal(service.href)}
                    className="inline-flex items-center text-pentagon-blue font-semibold hover:text-pentagon-hover transition-colors duration-200"
                  >
                    Learn More
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                  </button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* Why Choose Us */}
      <Section className="bg-slate-50">
        <SectionHeader
          subtitle="Why Choose Pentagon Advocates"
          title="Your Trusted Legal Partner"
          description="We combine legal expertise with personalized service to deliver exceptional results for our clients."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {whyChooseUs.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-pentagon-blue rounded-xl flex items-center justify-center mx-auto mb-6">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">{item.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{item.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Process Overview */}
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-0">
          <CardContent className="p-8 lg:p-12">
            <div className="text-center mb-8">
              <h3 className="text-2xl lg:text-3xl font-bold text-slate-900 mb-4">
                Our Legal Process
              </h3>
              <p className="text-slate-600 leading-relaxed max-w-2xl mx-auto">
                We follow a structured approach to ensure the best possible outcomes for our clients.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-pentagon-blue text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  1
                </div>
                <h4 className="font-semibold text-slate-900 mb-2">Consultation</h4>
                <p className="text-sm text-slate-600">Initial meeting to understand your needs</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-pentagon-blue text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  2
                </div>
                <h4 className="font-semibold text-slate-900 mb-2">Analysis</h4>
                <p className="text-sm text-slate-600">Thorough review of your legal situation</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-pentagon-blue text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  3
                </div>
                <h4 className="font-semibold text-slate-900 mb-2">Strategy</h4>
                <p className="text-sm text-slate-600">Development of tailored legal strategy</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-pentagon-blue text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  4
                </div>
                <h4 className="font-semibold text-slate-900 mb-2">Execution</h4>
                <p className="text-sm text-slate-600">Implementation and ongoing support</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </Section>

      {/* Service Modals */}
      <ServiceModal
        isOpen={activeModal === '/services/land-law'}
        onClose={() => setActiveModal(null)}
        title="Land Law & Litigation"
        breadcrumbs={['Services', 'Land Law & Litigation']}
      >
        <LandLawContent />
      </ServiceModal>

      <ServiceModal
        isOpen={activeModal === '/services/real-estate'}
        onClose={() => setActiveModal(null)}
        title="Real Estate Law"
        breadcrumbs={['Services', 'Real Estate Law']}
      >
        <RealEstateContent />
      </ServiceModal>

      <ServiceModal
        isOpen={activeModal === '/services/estate-planning'}
        onClose={() => setActiveModal(null)}
        title="Estate Planning"
        breadcrumbs={['Services', 'Estate Planning']}
      >
        <EstatePlanningContent />
      </ServiceModal>

      <ServiceModal
        isOpen={activeModal === '/services/family-law'}
        onClose={() => setActiveModal(null)}
        title="Family Law"
        breadcrumbs={['Services', 'Family Law']}
      >
        <FamilyLawContent />
      </ServiceModal>
    </>
  );
};

export default Services;
