# Pentagon Advocates - Production Deployment Guide

## Pre-Deployment Checklist

### ✅ Code Quality & Performance
- [x] All TypeScript errors resolved
- [x] ESLint warnings addressed
- [x] Production build optimizations configured
- [x] Code splitting and chunk optimization implemented
- [x] Console logs removed in production build
- [x] Source maps disabled for production

### ✅ SEO & Meta Tags
- [x] Comprehensive meta tags in index.html
- [x] Open Graph tags for social media sharing
- [x] Twitter Card meta tags
- [x] Structured data (JSON-LD) for search engines
- [x] Updated sitemap.xml with all pages and blog posts
- [x] Robots.txt configured for search engine crawling
- [x] Canonical URLs set

### ✅ Content & Images
- [x] All images optimized and properly sized
- [x] Favicon and logo files in place
- [x] Blog posts populated with actual content
- [x] Contact information verified and updated
- [x] All placeholder content replaced

### ✅ Responsive Design
- [x] Mobile-first responsive design implemented
- [x] All pages tested on mobile devices
- [x] Touch-friendly navigation and buttons
- [x] Proper viewport meta tag configured

### ✅ Performance Optimizations
- [x] Lazy loading for images
- [x] Code splitting for better load times
- [x] Vendor chunks separated for caching
- [x] Minification enabled
- [x] Tree shaking configured

## Build Commands

### Development Build
```bash
npm run build:dev
```

### Production Build
```bash
npm run build:prod
```

### Full Deployment Preparation
```bash
npm run deploy:prep
```

### Preview Production Build
```bash
npm run preview:prod
```

## Deployment Steps

1. **Run Pre-deployment Checks**
   ```bash
   npm run deploy:prep
   ```

2. **Verify Build Output**
   - Check `dist/` folder for all assets
   - Verify file sizes are optimized
   - Test the preview build locally

3. **Upload to Web Server**
   - Upload entire `dist/` folder contents to web root
   - Ensure proper file permissions
   - Configure web server for SPA routing

4. **Post-Deployment Verification**
   - Test all pages and navigation
   - Verify contact forms work
   - Check mobile responsiveness
   - Test page load speeds
   - Verify SEO meta tags in browser

## Web Server Configuration

### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.html;
}

# Enable gzip compression
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Set cache headers
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## Environment Variables

Production environment variables are configured in `.env.production`:
- Contact information
- Social media URLs
- Analytics configuration (when ready)
- Performance settings

## Security Considerations

- [x] No sensitive data in client-side code
- [x] Proper HTTPS configuration required
- [x] Content Security Policy headers recommended
- [x] Regular security updates for dependencies

## Performance Monitoring

After deployment, monitor:
- Page load times
- Core Web Vitals
- Mobile performance
- Search engine indexing
- Contact form submissions

## Support & Maintenance

- Regular dependency updates
- Content updates through blog posts
- SEO monitoring and optimization
- Performance monitoring and optimization
