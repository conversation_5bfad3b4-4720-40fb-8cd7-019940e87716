import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Phone, Mail, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigation = [
    { name: 'Home', href: '/' },
    {
      name: 'About',
      href: '/about',
      hasDropdown: true,
      dropdownItems: [
        { name: 'Who We Are', href: '/about' },
        { name: 'Our Team', href: '/about/team' }
      ]
    },
    { name: 'Services', href: '/services' },
    { name: 'Blog', href: '/blog' },
    { name: 'Contact', href: '/contact' },
  ];

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true;
    if (path !== '/' && location.pathname.startsWith(path)) return true;
    return false;
  };

  const isHomePage = location.pathname === '/';
  const shouldBeTransparent = isHomePage && !isScrolled;
  const shouldUseWhiteText = shouldBeTransparent;

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      {/* Top Bar - Hidden on home page when transparent */}
      {!shouldBeTransparent && (
        <div className="bg-pentagon-blue text-white py-2 px-4 hidden md:block">
          <div className="container mx-auto flex justify-between items-center text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span>+256 705 236 743</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
            </div>
            <div className="text-amber-400">
              Kampala • Uganda
            </div>
          </div>
        </div>
      )}

      {/* Main Header */}
      <header
        className={`transition-all duration-300 ${
          shouldBeTransparent
            ? 'bg-transparent'
            : isScrolled
            ? 'bg-white/95 backdrop-blur-md shadow-lg'
            : 'bg-white'
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3">
              <img
                src="/logo.png"
                alt="Pentagon Advocates Logo"
                className="h-12 lg:h-14 w-auto"
              />
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              {navigation.map((item) => (
                item.hasDropdown ? (
                  <DropdownMenu key={item.name}>
                    <DropdownMenuTrigger asChild>
                      <button
                        className={`flex items-center space-x-1 text-sm font-medium transition-colors duration-300 ${
                          isActive(item.href)
                            ? shouldUseWhiteText
                              ? 'text-white border-b-2 border-white pb-1 hover:text-white/80'
                              : 'text-pentagon-blue border-b-2 border-pentagon-blue pb-1 hover:text-pentagon-hover'
                            : shouldUseWhiteText
                            ? 'text-white/90 hover:text-white'
                            : 'text-slate-700 hover:text-pentagon-hover'
                        }`}
                      >
                        <span>{item.name}</span>
                        <ChevronDown className="w-4 h-4" />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-48">
                      {item.dropdownItems?.map((dropdownItem) => (
                        <DropdownMenuItem key={dropdownItem.name} asChild>
                          <Link
                            to={dropdownItem.href}
                            className="w-full cursor-pointer"
                          >
                            {dropdownItem.name}
                          </Link>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`text-sm font-medium transition-colors duration-300 ${
                      isActive(item.href)
                        ? shouldUseWhiteText
                          ? 'text-white border-b-2 border-white pb-1 hover:text-white/80'
                          : 'text-pentagon-blue border-b-2 border-pentagon-blue pb-1 hover:text-pentagon-hover'
                        : shouldUseWhiteText
                        ? 'text-white/90 hover:text-white'
                        : 'text-slate-700 hover:text-pentagon-hover'
                    }`}
                  >
                    {item.name}
                  </Link>
                )
              ))}
            </nav>

            {/* CTA Button */}
            <div className="hidden lg:flex items-center space-x-4">
              <Button
                asChild
                className={`px-6 py-2 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl ${
                  shouldUseWhiteText
                    ? 'bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white hover:text-pentagon-blue'
                    : 'bg-pentagon-blue hover:bg-pentagon-hover hover:text-white text-white/60'
                }`}
              >
                <Link to="/contact">Free Consultation</Link>
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${
                shouldUseWhiteText
                  ? 'text-white hover:text-white/80 hover:bg-white/10'
                  : 'text-slate-700 hover:text-pentagon-hover hover:bg-slate-100'
              }`}
            >
              {isMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-white border-t border-slate-200 shadow-lg">
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-4">
                {navigation.map((item) => (
                  <div key={item.name}>
                    <Link
                      to={item.href}
                      onClick={() => setIsMenuOpen(false)}
                      className={`text-base font-medium transition-colors duration-200 hover:text-pentagon-hover py-2 block ${
                        isActive(item.href)
                          ? 'text-pentagon-blue border-l-4 border-pentagon-blue pl-4'
                          : 'text-slate-700'
                      }`}
                    >
                      {item.name}
                    </Link>
                    {item.hasDropdown && item.dropdownItems && (
                      <div className="ml-4 mt-2 space-y-2">
                        {item.dropdownItems.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.name}
                            to={dropdownItem.href}
                            onClick={() => setIsMenuOpen(false)}
                            className={`text-sm font-medium transition-colors duration-200 hover:text-pentagon-hover py-1 block ${
                              isActive(dropdownItem.href)
                                ? 'text-pentagon-blue border-l-2 border-pentagon-blue pl-3'
                                : 'text-slate-600 pl-3'
                            }`}
                          >
                            {dropdownItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                <div className="pt-4 border-t border-slate-200">
                  <Button
                    asChild
                    className="w-full bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue text-white py-3 rounded-lg font-medium"
                  >
                    <Link to="/contact" onClick={() => setIsMenuOpen(false)}>
                      Free Consultation
                    </Link>
                  </Button>
                </div>
              </nav>
            </div>
          </div>
        )}
      </header>
    </div>
  );
};

export default Header;
