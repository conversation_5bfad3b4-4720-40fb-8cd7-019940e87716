import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, CheckCircle, ArrowRight, FileText, Users, Shield, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Section, SectionHeader } from '@/components/ui/section';

export const FamilyLawContent = () => {
  const services = [
    {
      title: "Divorce Proceedings",
      description: "Compassionate legal representation through divorce proceedings with focus on fair outcomes."
    },
    {
      title: "Child Custody & Support",
      description: "Protecting children's best interests in custody arrangements and support determinations."
    },
    {
      title: "Marriage Contracts",
      description: "Drafting prenuptial and postnuptial agreements to protect both parties' interests."
    },
    {
      title: "Spousal Support",
      description: "Ensuring fair spousal support arrangements that consider all relevant factors."
    },
    {
      title: "Adoption Services",
      description: "Legal guidance through the adoption process to expand your family legally and safely."
    },
    {
      title: "Domestic Violence Protection",
      description: "Immediate legal protection for victims of domestic violence and abuse."
    }
  ];

  const process = [
    {
      step: "1",
      title: "Confidential Consultation",
      description: "We provide a safe space to discuss your family law concerns and options."
    },
    {
      step: "2",
      title: "Case Assessment",
      description: "Thorough evaluation of your situation and development of legal strategy."
    },
    {
      step: "3",
      title: "Legal Action",
      description: "Implementation of legal proceedings or negotiations as appropriate."
    },
    {
      step: "4",
      title: "Ongoing Support",
      description: "Continued legal support and guidance throughout the process."
    }
  ];

  const benefits = [
    {
      icon: Shield,
      title: "Legal Protection",
      description: "Protect your rights and interests in all family law matters."
    },
    {
      icon: Users,
      title: "Compassionate Support",
      description: "Sensitive handling of family matters with understanding and empathy."
    },
    {
      icon: Clock,
      title: "Timely Resolution",
      description: "Efficient handling of family law cases to minimize stress and uncertainty."
    },
    {
      icon: FileText,
      title: "Confidential Service",
      description: "Complete confidentiality and discretion in all family law matters."
    }
  ];

  return (
    <>
      {/* Services Overview */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Our Family Law Services"
          title="Supporting Families Through Legal Challenges"
          description="We provide compassionate legal support for all family law matters, prioritizing the best interests of children and fair outcomes for all parties."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-pentagon-blue rounded-lg flex items-center justify-center mb-4">
                  <Heart className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-slate-900 mb-3">{service.title}</h3>
                <p className="text-slate-600 leading-relaxed">{service.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Process Section */}
      <Section className="bg-slate-50">
        <SectionHeader
          subtitle="Our Process"
          title="How We Handle Family Law Matters"
          description="Our compassionate approach ensures sensitive handling of family law issues while protecting your legal rights."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {process.map((item, index) => (
            <Card key={index} className="text-center border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="w-16 h-16 bg-pentagon-blue text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h3 className="text-lg font-bold text-slate-900 mb-3">{item.title}</h3>
                <p className="text-slate-600 leading-relaxed">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Benefits Section */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Why Choose Us"
          title="Benefits of Our Family Law Services"
          description="Pentagon Advocates provides compassionate and effective family law representation with focus on positive outcomes."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => {
            const IconComponent = benefit.icon;
            return (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-pentagon-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-slate-900 mb-3">{benefit.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{benefit.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* Expertise Section */}
      <Section className="bg-slate-50">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <SectionHeader
              subtitle="Our Expertise"
              title="Comprehensive Family Law Knowledge"
              description="Our team has extensive experience in all aspects of family law, providing compassionate and effective representation."
              centered={false}
            />
            
            <div className="space-y-4 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Divorce & Separation</h4>
                  <p className="text-slate-600 text-sm">Compassionate representation through divorce proceedings and separation agreements.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Child Custody</h4>
                  <p className="text-slate-600 text-sm">Protecting children's best interests in custody and visitation arrangements.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Domestic Relations</h4>
                  <p className="text-slate-600 text-sm">Comprehensive support for all domestic relations matters and family disputes.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Protection Orders</h4>
                  <p className="text-slate-600 text-sm">Immediate legal protection for victims of domestic violence and abuse.</p>
                </div>
              </div>
            </div>
            
            <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue">
              <Link to="/contact">Get Confidential Help</Link>
            </Button>
          </div>
          
          <div>
            <Card className="bg-white border-0 shadow-xl">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-slate-900 mb-6">Family Law Matters We Handle</h3>
                <div className="space-y-4">
                  {[
                    "Divorce and legal separation",
                    "Child custody and visitation",
                    "Child and spousal support",
                    "Property division",
                    "Prenuptial and postnuptial agreements",
                    "Adoption proceedings",
                    "Domestic violence protection",
                    "Paternity establishment"
                  ].map((matter, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <ArrowRight className="w-4 h-4 text-pentagon-blue flex-shrink-0" />
                      <span className="text-slate-700">{matter}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>
    </>
  );
};
