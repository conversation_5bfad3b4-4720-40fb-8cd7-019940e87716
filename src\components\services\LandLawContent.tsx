import React from 'react';
import { Link } from 'react-router-dom';
import { Scale, CheckCircle, ArrowRight, FileText, Users, Shield, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Section, SectionHeader } from '@/components/ui/section';

export const LandLawContent = () => {
  const services = [
    {
      title: "Land Dispute Resolution",
      description: "Expert representation in boundary disputes, ownership conflicts, and property rights matters."
    },
    {
      title: "Title Verification & Registration",
      description: "Comprehensive title searches, verification services, and assistance with land registration."
    },
    {
      title: "Customary Land Rights",
      description: "Specialized expertise in customary land tenure and traditional land ownership matters."
    },
    {
      title: "Land Acquisition",
      description: "Legal support for land acquisition processes, including due diligence and documentation."
    },
    {
      title: "Property Rights Protection",
      description: "Safeguarding your property rights through proper legal documentation and representation."
    },
    {
      title: "Land Use Planning",
      description: "Guidance on zoning regulations, land use permits, and development approvals."
    }
  ];

  const process = [
    {
      step: "1",
      title: "Initial Consultation",
      description: "We assess your land law needs and provide preliminary advice on your legal options."
    },
    {
      step: "2",
      title: "Due Diligence",
      description: "Comprehensive investigation of land records, titles, and any potential legal issues."
    },
    {
      step: "3",
      title: "Legal Strategy",
      description: "Development of a tailored legal strategy based on your specific circumstances."
    },
    {
      step: "4",
      title: "Implementation",
      description: "Execution of legal proceedings, documentation, or negotiations as required."
    }
  ];

  const benefits = [
    {
      icon: Shield,
      title: "Property Protection",
      description: "Secure your land rights with proper legal documentation and representation."
    },
    {
      icon: Users,
      title: "Expert Guidance",
      description: "Benefit from our extensive experience in Ugandan land law and local practices."
    },
    {
      icon: Clock,
      title: "Efficient Resolution",
      description: "Timely handling of land matters to minimize delays and complications."
    },
    {
      icon: FileText,
      title: "Comprehensive Documentation",
      description: "Thorough preparation of all necessary legal documents and filings."
    }
  ];

  return (
    <>
      {/* Services Overview */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Our Land Law Services"
          title="Comprehensive Legal Solutions"
          description="We offer a full range of land law services to protect your property rights and resolve land-related disputes."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-pentagon-blue rounded-lg flex items-center justify-center mb-4">
                  <Scale className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-slate-900 mb-3">{service.title}</h3>
                <p className="text-slate-600 leading-relaxed">{service.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Process Section */}
      <Section className="bg-slate-50">
        <SectionHeader
          subtitle="Our Process"
          title="How We Handle Land Law Matters"
          description="Our structured approach ensures thorough handling of your land law needs from initial consultation to final resolution."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {process.map((item, index) => (
            <Card key={index} className="text-center border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="w-16 h-16 bg-pentagon-blue text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h3 className="text-lg font-bold text-slate-900 mb-3">{item.title}</h3>
                <p className="text-slate-600 leading-relaxed">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Benefits Section */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Why Choose Us"
          title="Benefits of Our Land Law Services"
          description="Pentagon Advocates brings extensive experience and local expertise to every land law matter."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => {
            const IconComponent = benefit.icon;
            return (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-pentagon-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-slate-900 mb-3">{benefit.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{benefit.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* Expertise Section */}
      <Section className="bg-slate-50">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <SectionHeader
              subtitle="Our Expertise"
              title="Deep Understanding of Ugandan Land Law"
              description="Our team has extensive experience in all aspects of Ugandan land law, from customary tenure to modern property transactions."
              centered={false}
            />
            
            <div className="space-y-4 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Customary Land Expertise</h4>
                  <p className="text-slate-600 text-sm">Deep understanding of traditional land tenure systems and customary law.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Statutory Law Knowledge</h4>
                  <p className="text-slate-600 text-sm">Comprehensive knowledge of Uganda's Land Act and related legislation.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Dispute Resolution</h4>
                  <p className="text-slate-600 text-sm">Proven track record in resolving complex land disputes through various mechanisms.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Local Knowledge</h4>
                  <p className="text-slate-600 text-sm">Understanding of local practices and regional variations in land law application.</p>
                </div>
              </div>
            </div>
            
            <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue">
              <Link to="/contact">Discuss Your Land Law Needs</Link>
            </Button>
          </div>
          
          <div>
            <Card className="bg-white border-0 shadow-xl">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-slate-900 mb-6">Common Land Law Issues We Handle</h3>
                <div className="space-y-4">
                  {[
                    "Boundary disputes between neighbors",
                    "Fraudulent land transactions",
                    "Inheritance and succession disputes",
                    "Land grabbing and illegal occupation",
                    "Title defects and registration issues",
                    "Conflicts between customary and statutory rights",
                    "Land acquisition for development projects",
                    "Zoning and land use violations"
                  ].map((issue, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <ArrowRight className="w-4 h-4 text-pentagon-blue flex-shrink-0" />
                      <span className="text-slate-700">{issue}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>
    </>
  );
};
