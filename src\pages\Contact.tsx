
import { MapPin, Phone, Mail, Clock, CheckCircle, Home, ChevronRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Section, SectionHeader } from '@/components/ui/section';

const Contact = () => {

  const contactInfo = [
    {
      icon: MapPin,
      title: "Our Office",
      details: [
        "Suite JKM03, 1st Floor",
        "jokam House, Rubaga Road",
        "P.O. Box 111263",
        "Kampala, Uganda"
      ]
    },
    {
      icon: Phone,
      title: "Phone Numbers",
      details: [
        "Primary: +256 705 236 743",
        "Secondary: +256 786 236 743",
        "Available Mon-Fri 8AM-5PM"
      ]
    },
    {
      icon: Mail,
      title: "Email Us",
      details: [
        "<EMAIL>",
        "Quick response guaranteed",
        "Professional consultation available"
      ]
    },
    {
      icon: Clock,
      title: "Office Hours",
      details: [
        "Monday - Friday: 8:00 AM - 5:00 PM",
        "Saturday: 9:00 AM - 1:00 PM",
        "Sunday: Closed",
        "Emergency consultations available"
      ]
    }
  ];



  return (
    <>
      {/* Grad<PERSON> Header */}
      <div className="bg-gradient-to-br from-pentagon-blue via-slate-800 to-pentagon-blue text-white py-12 px-6 lg:px-12">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Left: Breadcrumbs */}
            <div className="order-2 lg:order-1">
              <nav className="flex items-center space-x-2 text-sm text-slate-300 mb-4">
                <Home className="w-4 h-4" />
                <ChevronRight className="w-4 h-4" />
                <span className="text-white font-medium">Contact</span>
              </nav>
            </div>

            {/* Right: Page Title */}
            <div className="order-1 lg:order-2 text-left lg:text-right">
              <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight">
                Hello!
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Get In Touch"
          title="Contact Information"
          description="Multiple ways to reach our legal team. Choose the method that works best for you."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {contactInfo.map((info, index) => {
            const IconComponent = info.icon;
            return (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-pentagon-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-slate-900 mb-4">{info.title}</h3>
                  <div className="space-y-2">
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-slate-600 text-sm">
                        {detail}
                      </p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* Why Choose Us Section */}
      <Section className="bg-slate-50">
        {/* Left: Header and Description */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <SectionHeader
            subtitle="Why Choose Pentagon Advocates"
            title="Your Trusted Legal Partners"
            description="We are committed to providing exceptional legal services with a client-focused approach and proven track record of success."
            centered={false}
          />

          <div className="mt-8">
            <h3 className="text-xl font-bold text-slate-900 mb-6">Our Commitment to Excellence</h3>
            <ul className="space-y-4 text-slate-700">
              <li className="flex items-start">
                <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <span className="font-semibold">Free Initial Consultation</span>
                  <p className="text-sm text-slate-600 mt-1">No cost to discuss your legal needs and understand your options with our expert team.</p>
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <span className="font-semibold">Expert Legal Team</span>
                  <p className="text-sm text-slate-600 mt-1">Experienced lawyers with deep knowledge of Ugandan law and international best practices.</p>
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <span className="font-semibold">Client-Focused Approach</span>
                  <p className="text-sm text-slate-600 mt-1">Personalized legal solutions tailored to your specific needs and circumstances.</p>
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <span className="font-semibold">Proven Track Record</span>
                  <p className="text-sm text-slate-600 mt-1">Successful outcomes for clients across all practice areas with measurable results.</p>
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <span className="font-semibold">Transparent Communication</span>
                  <p className="text-sm text-slate-600 mt-1">Regular updates and clear explanations throughout your legal journey.</p>
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <span className="font-semibold">Competitive Pricing</span>
                  <p className="text-sm text-slate-600 mt-1">Fair and transparent pricing with no hidden fees or surprise charges.</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </Section>

      {/* Emergency Services Section */}
      <Section className="bg-white border-t-4 border-pentagon-blue">
        <div className="max-w-4xl mx-auto">
          <SectionHeader
            subtitle="Emergency Legal Services"
            title="24/7 Legal Support When You Need It Most"
            description="For urgent legal matters outside business hours, our emergency services ensure you get the help you need."
            centered={true}
          />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mt-12">
            {/* Emergency Contact Information */}
            <div className="bg-pentagon-blue text-white p-8 rounded-xl">
              <h3 className="text-2xl font-bold mb-6 flex items-center">
                <Phone className="w-8 h-8 mr-3 text-blue-300" />
                Emergency Contact
              </h3>
              <div className="space-y-4">
                <div className="text-center py-6 bg-white/10 rounded-lg">
                  <p className="text-blue-200 text-sm mb-2">Emergency Hotline</p>
                  <p className="text-3xl font-bold text-white">+256 705 236 743</p>
                  <p className="text-blue-200 text-sm mt-2">Available 24/7 for urgent matters</p>
                </div>
                <div className="text-center py-4 bg-white/5 rounded-lg">
                  <p className="text-blue-200 text-sm mb-1">Priority Email</p>
                  <p className="text-lg font-semibold text-white"><EMAIL></p>
                </div>
              </div>
            </div>

            {/* Emergency Services List */}
            <div>
              <h3 className="text-2xl font-bold text-slate-900 mb-6">What Qualifies as Emergency?</h3>
              <ul className="space-y-4 text-slate-700">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Arrest or detention</strong> - Immediate legal representation needed</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Court injunctions</strong> - Time-sensitive court orders or restraining orders</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Property seizure</strong> - Urgent property or asset protection matters</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Contract disputes</strong> - Critical business agreements requiring immediate action</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Family emergencies</strong> - Child custody or domestic violence situations</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pentagon-blue mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Immigration issues</strong> - Deportation or visa emergencies</span>
                </li>
              </ul>

              <div className="mt-8 p-4 bg-slate-50 rounded-lg border-l-4 border-pentagon-blue">
                <h4 className="font-bold text-slate-900 mb-2">Response Guarantee</h4>
                <p className="text-slate-600 text-sm">
                  We respond to all emergency calls within <strong>2 hours</strong>, even outside regular business hours.
                  Leave a detailed message and we'll contact you as soon as possible.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Section>
    </>
  );
};

export default Contact;
