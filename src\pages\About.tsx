import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Users, Award, Heart, Target, Eye, Lightbulb, Shield, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Section, SectionHeader } from '@/components/ui/section';

const About = () => {
  const values = [
    {
      icon: Target,
      title: "Sustainability",
      description: "We provide practical, investment-focused legal advice to foster sustainable businesses and communities."
    },
    {
      icon: Shield,
      title: "Integrity",
      description: "Excellence and honesty underpin every professional solution we offer to our clients."
    },
    {
      icon: Award,
      title: "Reliability",
      description: "Quality services delivered with determination and meticulous care, every time."
    }
  ];



  return (
    <>
      {/* Gradient Header */}
      <div className="bg-gradient-to-br from-pentagon-blue via-slate-800 to-pentagon-blue text-white py-12 px-6 lg:px-12">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Left: Breadcrumbs */}
            <div className="order-2 lg:order-1">
              <nav className="flex items-center space-x-2 text-sm text-slate-300 mb-4">
                <Home className="w-4 h-4" />
                <ChevronRight className="w-4 h-4" />
                <span className="text-white font-medium">About</span>
              </nav>
              <p className="text-slate-300 text-lg leading-relaxed">
                We are a dynamic and forward-thinking law firm committed to delivering innovative, client-centered solutions that drive sustainable growth and integrity in the legal sector.
              </p>
            </div>

            {/* Right: Page Title */}
            <div className="order-1 lg:order-2 text-left lg:text-right">
              <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight">
                Who We Are
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Our Story Section */}
      <Section className="bg-white">
        <div className="max-w-7xl mx-auto">
          <Card className="shadow-lg border-0 overflow-hidden">
            <CardContent className="p-0">
              <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[600px]">
                {/* Left Content */}
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="text-left">
                    <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">Our Story</h2>
                    <p className="text-lg text-slate-600 mb-6 leading-relaxed">
                      Pentagon Advocates was established in 2021 by Sengooba Farooq, a young visionary with a passion for excellence in legal practice.
                    </p>

                    <p className="text-slate-600 leading-relaxed mb-6">
                      As a firm deeply rooted in Uganda, we understand the unique legal landscape of the region and are
                      dedicated to providing tailored legal services that meet the needs of our diverse clientele. Our
                      specialization in Land Law, Real Estate, and Domestic Relations positions us as trusted advisors
                      for individuals and businesses seeking reliable and innovative legal solutions.
                    </p>

                    <p className="text-slate-600 leading-relaxed mb-8">
                      We combine youthful energy with deep expertise to address complex legal challenges, fostering a
                      collaborative environment where innovation and continuous learning are encouraged. This allows us
                      to stay at the forefront of legal developments while maintaining the highest standards of practice.
                    </p>

                    <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-white">
                      <Link to="/contact">Schedule a Consultation</Link>
                    </Button>
                  </div>
                </div>

                {/* Right Floating Image with Slanted Design */}
                <div className="relative lg:block hidden">
                  <div
                    className="absolute inset-0 bg-gradient-to-br from-pentagon-blue to-slate-700"
                    style={{
                      clipPath: 'polygon(15% 0%, 100% 0%, 100% 100%, 0% 100%)'
                    }}
                  >
                    <div className="absolute inset-0 bg-black/20"></div>
                    <img
                      src="/law.jpg"
                      alt="Pentagon Advocates Team"
                      className="w-full h-full object-cover"
                      style={{
                        clipPath: 'polygon(15% 0%, 100% 0%, 100% 100%, 0% 100%)'
                      }}
                    />
                    <div className="absolute inset-0 bg-pentagon-blue/10"></div>
                  </div>
                </div>

                {/* Mobile Image */}
                <div className="lg:hidden">
                  <img
                    src="/law.jpg"
                    alt="Pentagon Advocates Team"
                    className="w-full h-64 object-cover"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </Section>

      {/* Values Section */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Our Values"
          title="What Drives Us Forward"
          description="Our core values guide every decision we make and every service we provide to our clients."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {values.map((value, index) => {
            const IconComponent = value.icon;
            return (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-gradient-to-br from-pentagon-blue to-pentagon-blue rounded-xl flex items-center justify-center mx-auto mb-6">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">{value.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* Mission & Vision Section */}
      <Section className="bg-pentagon-blue text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 max-w-6xl mx-auto">
            {/* Mission Card */}
            <Card className="bg-white/10 backdrop-blur-sm border-0 hover:bg-white/15 transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-white/20 rounded-full mr-4">
                    <Target className="w-6 h-6 text-pale-grey" />
                  </div>
                  <h3 className="text-2xl font-bold text-white tracking-tight">Our Mission</h3>
                </div>
                <p className="text-pale-grey/90 text-lg leading-relaxed">
                  <span className="block italic text-white font-medium mb-2">"Leveraging prudent legal services</span>
                  for sustainable business success and client prosperity through innovative, 
                  ethical legal solutions."
                </p>
              </CardContent>
            </Card>
            
            {/* Vision Card */}
            <Card className="bg-white/10 backdrop-blur-sm border-0 hover:bg-white/15 transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-white/20 rounded-full mr-4">
                    <Eye className="w-6 h-6 text-pale-grey" />
                  </div>
                  <h3 className="text-2xl font-bold text-white tracking-tight">Our Vision</h3>
                </div>
                <p className="text-pale-grey/90 text-lg leading-relaxed">
                  <span className="block italic text-white font-medium mb-2">"To be the leading provider</span>
                  of high-quality, client-centric legal solutions where expertise 
                  and integrity empower clients to navigate legal challenges with confidence 
                  and achieve their strategic objectives."
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>

      {/* Innovation & Community Section */}
      <Section className="bg-slate-50">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div>
            <div className="flex items-center mb-6">
              <Lightbulb className="w-8 h-8 text-amber-500 mr-3" />
              <h3 className="text-2xl font-bold text-slate-900">Innovation in Legal Practice</h3>
            </div>
            <p className="text-slate-600 leading-relaxed mb-6">
              At Pentagon Advocates, we embrace technology to enhance our legal services. From advanced case 
              management systems to digital communication tools, we leverage innovation to improve efficiency, 
              accuracy, and client experience.
            </p>
            <p className="text-slate-600 leading-relaxed">
              Our commitment to staying updated with the latest legal technologies ensures that we provide 
              cutting-edge solutions to our clients, blending traditional legal expertise with modern practices.
            </p>
          </div>
          
          <div>
            <div className="flex items-center mb-6">
              <Heart className="w-8 h-8 text-pink-500 mr-3" />
              <h3 className="text-2xl font-bold text-slate-900">Community Involvement</h3>
            </div>
            <p className="text-slate-600 leading-relaxed mb-6">
              Pentagon Advocates is deeply committed to giving back to the community. We actively participate 
              in pro bono work, providing legal assistance to those in need. Additionally, we engage in legal 
              education initiatives, organizing workshops and seminars.
            </p>
            <p className="text-slate-600 leading-relaxed">
              Our involvement in events such as the Rule of Law week and our support for legal scholarship 
              reflect our dedication to fostering a more informed and empowered society.
            </p>
          </div>
        </div>
      </Section>
    </>
  );
};

export default About;
