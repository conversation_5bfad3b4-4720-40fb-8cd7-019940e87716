import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Users, Award, Heart, Target, Eye, Lightbulb, Shield, Home, ChevronRight, Scale } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Section, SectionHeader } from '@/components/ui/section';
import TeamMemberModal from '@/components/ui/team-member-modal';

const Team = () => {
  const [selectedMember, setSelectedMember] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (member: any) => {
    setSelectedMember(member);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setSelectedMember(null);
    setIsModalOpen(false);
  };

  const teamMembers = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON> Farooq",
      position: "Founder & Managing Partner, Head of Land Transactions",
      image: "/farooqb.jpg",
      bio: "A visionary legal practitioner with a passion for excellence in legal practice. Sengooba Farooq established Pentagon Advocates in 2021 with a commitment to delivering innovative, client-centered solutions that exceed client expectations.",
      specializations: [
        "Land Law & Litigation",
        "Real Estate Law",
        "Corporate Law",
        "Commercial Litigation",
        "Contract Law"
      ],
      education: [
        "Bachelor of Laws (LLB) - Islamic University of Uganda",
        "Diploma in Legal Practice - Law Development Centre",
        "Post Graduate Diploma in Tax and Revenue Administration - East African School of Taxation",
        "Certificate in Alternative Dispute Resolution"
      ],
      experience: "5+ Years Experience",
      email: "<EMAIL>",
      phone: "+*********** 743",
      achievements: [
        "Founded Pentagon Advocates in 2021",
        "Recognized as Top Legal Practitioner in Uganda 2023",
        "Expert in complex land disputes resolution"
      ],
      location: "Kampala, Uganda"
    },
    {
      id: 4,
      name: "Mujabi Evans Ssewayana",
      position: "Partner, Head Litigation",
      image: "/Evans.jpg",
      bio: "Mujabi Evans Ssewayana is an Advocate of the High Court of Uganda and all courts of Judicature, including the Supreme Court of Uganda. Over the years, Evans has developed a niche, as well as a unique understanding of the nuances relating to land law, employment law, intellectual property law, debt recovery and personal injury matters. He has a hands-on approach and is a legal practitioner with a business mind that prides himself in offering client-oriented and pragmatic solutions.",
      specializations: [
        "Land Law",
        "Employment Law",
        "Intellectual Property Law",
        "Debt Recovery",
        "Personal Injury",
        "Commercial Disputes",
        "Property Transactions"
      ],
      education: [
        "Bachelor of Laws (LLB)",
        "Postgraduate Diploma in Legal Practice - Law Development Centre"
      ],
      experience: "Partner, Head Litigation",
      email: "<EMAIL>",
      phone: "+*********** 743",
      achievements: [
        "Advocate of the High Court and Supreme Court of Uganda",
        "Member of Uganda Law Society",
        "Member of East African Law Society",
        "Expert in land and employment law matters"
      ],
      location: "Kampala, Uganda"
    },
    {
      id: 6,
      name: "Rashidah Zawedde",
      position: "Partner, Head of Family & Domestic Relations",
      image: "/Rashidah.jpg",
      bio: "Rashidah is an Advocate and a partner at the firm. She holds a Bachelor of Laws Degree from Islamic University In Uganda (Kampala Campus) and a Post-Graduate Diploma in Legal Practice from Law development Center Mbarara. She is the current Head of Family Department, a position she has been elevated to over her stay at the firm. Prior to joining the firm, Rashidah worked with Kitatta Adikin & Co. Advocates as an intern and also practiced from the same firm for a period of 1 year before joining Pentagon Advocates.",
      specializations: [
        "Family Law",
        "Land Transactions",
        "Corporate and Commercial Practice",
        "Civil Litigation",
        "Alternative Dispute Resolution",
        "Domestic Relations"
      ],
      education: [
        "Bachelor of Laws (LLB Hons) - Islamic University In Uganda",
        "Postgraduate Diploma in Legal Practice - Law Development Centre Mbarara"
      ],
      experience: "Partner",
      email: "<EMAIL>",
      phone: "+*********** 743",
      achievements: [
        "Advocate of the High Court of Uganda",
        "Head of Family Department at Pentagon Advocates",
        "Member of Uganda Law Society",
        "Member of East African Law Society",
        "Expert in family law and domestic relations"
      ],
      location: "Kampala, Uganda"
    },
    {
      id: 3,
      name: "Alibankoha Rebecca",
      position: "Associate Partner, Head Corporate & Commercial",
      image: "/rebecca.jpg",
      bio: "Alibankoha Rebecca is an Advocate of the High Court of Uganda and all subordinate courts, currently serving as an Associate Partner at Pentagon Advocates. Her legal practice focuses on corporate and commercial law, land transactions, family law, and litigation. She has developed a strong reputation for delivering client-centered solutions with experience advising individuals, businesses, and institutions.",
      specializations: [
        "Corporate & Commercial Law",
        "Land Transactions",
        "Family Law",
        "Litigation",
        "Regulatory Compliance",
        "Corporate Structuring",
        "Dispute Resolution"
      ],
      education: [
        "Bachelor of Laws - Uganda Christian University",
        "Postgraduate Diploma in Legal Practice - Law Development Centre"
      ],
      experience: "Associate Partner, Head Corporate & Commercial",
      email: "<EMAIL>",
      phone: "+*********** 743",
      achievements: [
        "Advocate of the High Court of Uganda",
        "Expert in corporate and commercial law",
        "Proven track record in land transactions",
        "Committed to professional excellence"
      ],
      location: "Kampala, Uganda"
    },
    {
      id: 2,
      name: "Ntege Jonathan Solomon",
      position: "Associate Partner",
      image: "/Ntege.jpg",
      bio: "Ntege Jonathan Solomon is an Associate Partner at Pentagon Advocates, with a growing reputation for diligence, analytical depth, and a commitment to delivering practical legal solutions. He has developed experience in commercial transactions, land law, litigation, and legal compliance.",
      specializations: [
        "Commercial Transactions",
        "Land Law",
        "Litigation",
        "Legal Compliance",
        "Contract Law",
        "Property Disputes"
      ],
      education: [
        "Bachelor of Laws (LLB) - Makerere University",
        "Postgraduate Diploma in Legal Practice - Law Development Centre"
      ],
      experience: "Associate Partner",
      email: "<EMAIL>",
      phone: "+*********** 743",
      achievements: [
        "Expert in commercial transactions",
        "Skilled in land law matters",
        "Proven litigation experience",
        "Committed to practical legal solutions"
      ],
      location: "Kampala, Uganda"
    },
    {
      id: 5,
      name: "Nantongo Milly Kiwanuka",
      position: "Associate Partner",
      image: "/Milly.jpg",
      bio: "Nantongo Milly Kiwanuka is a passionate result oriented and dedicated Associate Partner at Pentagon Advocates with a strong academic foundation and a steadfast commitment to the legal profession and justice. Driven by a commitment to ethical practice, integrity, diligence and continuous learning, she thrives in collaborative environments and is dedicated to delivering high-quality legal solutions and client representation.",
      specializations: [
        "Family Law",
        "Marriage & Divorce",
        "Child Custody & Maintenance",
        "Inheritance Law",
        "Company Law",
        "Corporate Governance",
        "Company Incorporation & Regulation",
        "Statutory Compliance"
      ],
      education: [
        "Bachelor of Laws with Honours - Islamic University in Uganda (IUIU)",
        "Postgraduate Diploma in Legal Practice - Law Development Centre"
      ],
      experience: "Associate Partner",
      email: "<EMAIL>",
      phone: "+*********** 743",
      achievements: [
        "Passionate advocate for family rights",
        "Expert in corporate compliance matters",
        "Committed to ethical legal practice",
        "Dedicated to continuous professional development"
      ],
      location: "Kampala, Uganda"
    },
  ];

  const expertise = [
    {
      icon: Scale,
      title: "Legal Excellence",
      description: "Our team brings years of combined experience in various areas of law, ensuring comprehensive legal solutions."
    },
    {
      icon: Target,
      title: "Client-Focused",
      description: "We prioritize our clients' needs and work tirelessly to achieve the best possible outcomes for every case."
    },
    {
      icon: Shield,
      title: "Professional Integrity",
      description: "We maintain the highest standards of professional conduct and ethical practice in all our legal services."
    }
  ];

  return (
    <>
      {/* Gradient Header */}
      <div className="bg-gradient-to-br from-pentagon-blue via-slate-800 to-pentagon-blue text-white py-12 px-6 lg:px-12">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Left: Breadcrumbs */}
            <div className="order-2 lg:order-1">
              <nav className="flex items-center space-x-2 text-sm text-slate-300 mb-4">
                <Home className="w-4 h-4" />
                <ChevronRight className="w-4 h-4" />
                <Link to="/about" className="text-slate-300 hover:text-white transition-colors">
                  About
                </Link>
                <ChevronRight className="w-4 h-4" />
                <span className="text-white font-medium">Our Team</span>
              </nav>
              <p className="text-slate-300 text-lg leading-relaxed">
                Meet the dedicated legal professionals who make Pentagon Advocates a trusted partner for all your legal needs.
              </p>
            </div>

            {/* Right: Page Title */}
            <div className="order-1 lg:order-2 text-left lg:text-right">
              <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight">
                Our Team
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Team Members Section */}
      <Section className="bg-white">
        <SectionHeader
          title="Meet Our Team"
          description="Our experienced team of is committed to providing exceptional service and achieving the best outcomes for our clients."
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member) => (
            <Card key={member.id} className="shadow-lg border-0 overflow-hidden hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-0">
                {/* Image */}
                <div className="relative overflow-hidden aspect-square">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-pentagon-blue/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Content */}
                <div className="p-6 text-center">
                  <button
                    onClick={() => openModal(member)}
                    className="w-full text-center hover:text-pentagon-blue transition-colors duration-200"
                  >
                    <h3 className="text-xl font-bold text-slate-900 mb-2 hover:text-pentagon-blue transition-colors">
                      {member.name}
                    </h3>
                  </button>
                  <p className="text-pale-grey font-medium">{member.position}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Team Expertise Section */}
      <Section className="bg-slate-50">
        <SectionHeader
          title="Why Choose Our Team"
          description="Our team combines extensive legal knowledge with a commitment to client service and professional integrity."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {expertise.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-pentagon-blue rounded-xl flex items-center justify-center mx-auto mb-6">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">{item.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{item.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* Team Member Modal */}
      <TeamMemberModal
        member={selectedMember}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </>
  );
};

export default Team;
