<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Image Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
        }
        .placeholder-image {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            margin: 0 auto 10px;
            background: linear-gradient(135deg, #18213E, #4A5568);
        }
        button {
            background: #18213E;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2D3748;
        }
        .instructions {
            background: #E2E8F0;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Pentagon Advocates Team Image Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <p>1. Click "Generate All Images" to create placeholder images for all team members</p>
            <p>2. Right-click on each image and "Save image as..." to download</p>
            <p>3. Save them in the public/team/ directory with the exact filenames shown</p>
            <p>4. Replace these placeholders with actual professional photos later</p>
        </div>

        <button onclick="generateAllImages()">Generate All Images</button>
        <button onclick="downloadAll()">Download All as ZIP</button>

        <div class="image-grid" id="imageGrid">
            <!-- Images will be generated here -->
        </div>
    </div>

    <script>
        const teamMembers = [
            { name: "Sarah Nakamya", filename: "sarah.jpg", initials: "SN" },
            { name: "David Mukasa", filename: "david.jpg", initials: "DM" },
            { name: "Grace Namugga", filename: "grace.jpg", initials: "GN" },
            { name: "James Okello", filename: "james.jpg", initials: "JO" },
            { name: "Rebecca Atim", filename: "rebecca.jpg", initials: "RA" },
            { name: "Michael Ssebunya", filename: "michael.jpg", initials: "MS" },
            { name: "Patricia Namutebi", filename: "patricia.jpg", initials: "PN" },
            { name: "Andrew Kiggundu", filename: "andrew.jpg", initials: "AK" },
            { name: "Linda Nakirya", filename: "linda.jpg", initials: "LN" }
        ];

        function generateImage(member, index) {
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 400;
            const ctx = canvas.getContext('2d');

            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, 400, 400);
            const colors = [
                ['#18213E', '#4A5568'],
                ['#2D3748', '#4A5568'],
                ['#1A202C', '#2D3748'],
                ['#18213E', '#2A4365'],
                ['#2C5282', '#3182CE'],
                ['#1A365D', '#2C5282'],
                ['#2A4365', '#3182CE'],
                ['#1E4A8C', '#3182CE'],
                ['#18213E', '#1A365D']
            ];
            
            const colorPair = colors[index % colors.length];
            gradient.addColorStop(0, colorPair[0]);
            gradient.addColorStop(1, colorPair[1]);
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 400);

            // Add initials
            ctx.fillStyle = 'white';
            ctx.font = 'bold 80px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(member.initials, 200, 200);

            // Add subtle pattern
            ctx.globalAlpha = 0.1;
            ctx.fillStyle = 'white';
            for (let i = 0; i < 20; i++) {
                ctx.beginPath();
                ctx.arc(Math.random() * 400, Math.random() * 400, Math.random() * 3, 0, Math.PI * 2);
                ctx.fill();
            }

            return canvas;
        }

        function generateAllImages() {
            const grid = document.getElementById('imageGrid');
            grid.innerHTML = '';

            teamMembers.forEach((member, index) => {
                const canvas = generateImage(member, index);
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';
                
                imageItem.innerHTML = `
                    <h4>${member.name}</h4>
                    <p><strong>Filename:</strong> ${member.filename}</p>
                    <button onclick="downloadImage('${member.filename}', ${index})">Download ${member.filename}</button>
                `;
                
                imageItem.appendChild(canvas);
                grid.appendChild(imageItem);
            });
        }

        function downloadImage(filename, index) {
            const member = teamMembers[index];
            const canvas = generateImage(member, index);
            
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/jpeg', 0.9);
        }

        function downloadAll() {
            teamMembers.forEach((member, index) => {
                setTimeout(() => {
                    downloadImage(member.filename, index);
                }, index * 500); // Stagger downloads
            });
        }

        // Generate images on page load
        window.onload = function() {
            generateAllImages();
        };
    </script>
</body>
</html>
