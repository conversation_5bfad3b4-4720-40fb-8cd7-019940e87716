import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface HeroProps {
  title: string;
  subtitle?: string;
  description?: string;
  primaryCTA?: {
    text: string;
    href: string;
  };
  secondaryCTA?: {
    text: string;
    href: string;
  };
  backgroundImage?: string;
  backgroundSize?: string;
  backgroundRepeat?: string;
  backgroundPosition?: string;
  corporateGradient?: boolean;
  firmImage?: string;
  className?: string;
  children?: React.ReactNode;
}

export const Hero: React.FC<HeroProps> = ({
  title,
  subtitle,
  description,
  primaryCTA,
  secondaryCTA,
  backgroundImage,
  backgroundSize = 'cover',
  backgroundRepeat = 'no-repeat',
  backgroundPosition = 'center',
  corporateGradient = false,
  firmImage,
  className = '',
  children,
}) => {
  return (
    <section
      className={cn(
        'relative overflow-hidden',
        corporateGradient
          ? 'bg-gradient-to-br from-pentagon-blue via-slate-800 to-pentagon-blue text-white min-h-screen pt-0 flex items-end'
          : backgroundImage
          ? 'flex items-center justify-center py-8'
          : 'bg-gradient-to-br from-slate-50 to-slate-100 py-20 lg:py-32 flex items-end',
        className
      )}
    >
      {/* Title Background Image Container */}
      {backgroundImage && (
        <div
          className="relative flex items-end justify-center w-full"
          style={{
            backgroundImage: `url(${backgroundImage})`,
            backgroundSize,
            backgroundRepeat,
            backgroundPosition,
            minHeight: '300px',
            aspectRatio: '1584/396'
          }}
        >
          <div className="absolute inset-0 bg-pentagon-blue/60 flex items-end justify-center">
            <div className="container mx-auto px-4 pb-8 relative z-10">
              <div className="max-w-4xl mx-auto text-center text-white">
                {subtitle && (
                  <p className="font-semibold text-sm uppercase tracking-wide mb-4 text-pale-grey">
                    {subtitle}
                  </p>
                )}

                <h1 className="text-3xl lg:text-5xl xl:text-6xl font-bold mb-6 leading-tight text-white">
                  {title}
                </h1>

                {description && (
                  <p className="text-lg lg:text-xl mb-8 leading-relaxed max-w-3xl mx-auto text-slate-200">
                    {description}
                  </p>
                )}

                {(primaryCTA || secondaryCTA) && (
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    {primaryCTA && (
                      <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-200">
                        <Link to={primaryCTA.href}>{primaryCTA.text}</Link>
                      </Button>
                    )}

                    {secondaryCTA && (
                      <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-200 border-white text-white hover:bg-white hover:text-slate-900"
                      >
                        <Link to={secondaryCTA.href}>{secondaryCTA.text}</Link>
                      </Button>
                    )}
                  </div>
                )}

                {children && (
                  <div className="mt-8">
                    {children}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {!backgroundImage && (
        <>
          {corporateGradient && (
            <div className="absolute inset-0">
              <div className="absolute inset-0 opacity-20">
                <div className="absolute top-0 right-0 w-96 h-96 bg-pale-grey/10 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-pale-grey/5 rounded-full blur-2xl"></div>
              </div>

              {firmImage && (
                <div className="absolute inset-0 flex items-center justify-center opacity-40">
                  <img
                    src={firmImage}
                    alt="Pentagon Advocates Firm"
                    className="w-full h-full object-cover object-center"
                  />
                </div>
              )}

              <div className="absolute inset-0 bg-gradient-to-t from-pentagon-blue/90 via-pentagon-blue/30 to-transparent"></div>
            </div>
          )}

          {!corporateGradient && (
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-pentagon-blue/20 to-transparent"></div>
              <div className="absolute top-20 right-20 w-72 h-72 bg-pentagon-blue/10 rounded-full blur-3xl"></div>
              <div className="absolute bottom-20 left-20 w-96 h-96 bg-pale-grey/10 rounded-full blur-3xl"></div>
            </div>
          )}

          <div className={cn(
            "container mx-auto px-4 relative z-10",
            corporateGradient ? "pb-20" : ""
          )}>
            <div className="max-w-4xl mx-auto text-center">
              {subtitle && (
                <p className={cn(
                  'font-semibold text-sm uppercase tracking-wide mb-4',
                  corporateGradient ? 'text-pale-grey' : 'text-pentagon-blue'
                )}>
                  {subtitle}
                </p>
              )}

              <h1 className={cn(
                'text-4xl lg:text-6xl xl:text-7xl font-bold mb-6 leading-tight',
                corporateGradient ? 'text-white' : 'text-slate-900'
              )}>
                {title}
              </h1>

              {description && (
                <p className={cn(
                  'text-lg lg:text-xl mb-8 leading-relaxed max-w-3xl mx-auto',
                  corporateGradient ? 'text-slate-200' : 'text-slate-600'
                )}>
                  {description}
                </p>
              )}

              {(primaryCTA || secondaryCTA) && (
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  {primaryCTA && (
                    <Button
                      asChild
                      size="lg"
                      className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <Link to={primaryCTA.href}>{primaryCTA.text}</Link>
                    </Button>
                  )}

                  {secondaryCTA && (
                    <Button
                      asChild
                      variant="outline"
                      size="lg"
                      className={cn(
                        'px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-200',
                        corporateGradient
                          ? 'border-white text-white hover:bg-white hover:text-slate-900'
                          : 'border-pentagon-blue text-pentagon-blue hover:bg-pentagon-blue hover:text-white'
                      )}
                    >
                      <Link to={secondaryCTA.href}>{secondaryCTA.text}</Link>
                    </Button>
                  )}
                </div>
              )}

              {children && (
                <div className={cn(
                  "mt-12",
                  corporateGradient ? "mt-8" : ""
                )}>
                  {children}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </section>
  );
};
