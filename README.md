# Pentagon Advocates - Legal Services Website

## Project Overview

Pentagon Advocates is a premier law firm in Uganda specializing in Land Law, Real Estate, Estate Planning, and Family Law. This website serves as the digital presence for the firm, providing information about services, team members, and contact details.

## Development Setup

To work on this project locally, you'll need Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

### Local Development

```bash
# Clone the repository
git clone <repository-url>
cd pentagon-advocates-landing-page

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:analyze` - Build and analyze bundle size
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Technologies Used

This project is built with modern web technologies:

- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript
- **React** - UI library for building user interfaces
- **shadcn/ui** - Reusable component library
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Lucide React** - Beautiful icon library

## Features

- **Responsive Design** - Mobile-first approach with full responsive layout
- **Content Protection** - Advanced protection against content copying
- **Performance Optimized** - Optimized images and code splitting
- **SEO Friendly** - Proper meta tags and structured data
- **Accessibility** - WCAG compliant design patterns

## Deployment

The project can be deployed to any static hosting service:

1. Build the project: `npm run build`
2. Deploy the `dist` folder to your hosting service
3. Configure your domain and SSL certificate

Popular hosting options:
- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront
