/**
 * Content Protection Configuration
 * 
 * Centralized configuration for all content protection features
 * on the Pentagon Advocates website.
 */

export interface ContentProtectionConfig {
  // Global settings
  enabled: boolean;
  developmentMode: boolean;
  
  // Right-click protection
  disableRightClick: boolean;
  
  // Text selection protection
  disableTextSelection: boolean;
  allowInputSelection: boolean;
  
  // Keyboard shortcuts protection
  disableKeyboardShortcuts: boolean;
  disabledShortcuts: string[];
  
  // Drag and drop protection
  disableDragAndDrop: boolean;
  
  // Image protection
  disableImageSaving: boolean;
  disableImageDragging: boolean;
  
  // Developer tools protection
  disableDevTools: boolean;
  devToolsDetection: boolean;
  
  // Print protection
  disablePrinting: boolean;
  
  // Warning messages
  showWarningMessages: boolean;
  warningMessages: {
    rightClick: string;
    textSelection: string;
    keyboardShortcuts: string;
    imageSaving: string;
    devTools: string;
    general: string;
  };
  
  // Console protection
  showConsoleWarning: boolean;
  clearConsole: boolean;
}

// Default configuration
export const defaultProtectionConfig: ContentProtectionConfig = {
  // Global settings
  enabled: true,
  developmentMode: process.env.NODE_ENV === 'development',
  
  // Right-click protection
  disableRightClick: true,
  
  // Text selection protection
  disableTextSelection: true,
  allowInputSelection: true,
  
  // Keyboard shortcuts protection
  disableKeyboardShortcuts: true,
  disabledShortcuts: [
    'F12',           // Developer Tools
    'Ctrl+Shift+I',  // Developer Tools
    'Ctrl+Shift+J',  // Console
    'Ctrl+Shift+C',  // Inspect Element
    'Ctrl+U',        // View Source
    'Ctrl+A',        // Select All
    'Ctrl+S',        // Save Page
    'Ctrl+P',        // Print
    'Ctrl+C',        // Copy
    'Ctrl+X',        // Cut
    'Ctrl+V',        // Paste
  ],
  
  // Drag and drop protection
  disableDragAndDrop: true,
  
  // Image protection
  disableImageSaving: true,
  disableImageDragging: true,
  
  // Developer tools protection
  disableDevTools: true,
  devToolsDetection: true,
  
  // Print protection
  disablePrinting: true,
  
  // Warning messages
  showWarningMessages: true,
  warningMessages: {
    rightClick: 'Right-click is disabled to protect Pentagon Advocates content.',
    textSelection: 'Text selection is disabled to protect our content.',
    keyboardShortcuts: 'This keyboard shortcut is disabled for content protection.',
    imageSaving: 'Image saving is disabled to protect our visual content.',
    devTools: 'Developer tools detected. Please close them to continue browsing.',
    general: 'This content is protected by Pentagon Advocates. Unauthorized copying is prohibited.',
  },
  
  // Console protection
  showConsoleWarning: true,
  clearConsole: true,
};

// Production-specific overrides
export const productionProtectionConfig: Partial<ContentProtectionConfig> = {
  enabled: true,
  developmentMode: false,
  disableDevTools: true,
  devToolsDetection: true,
  showConsoleWarning: true,
  clearConsole: true,
};

// Development-specific overrides (less restrictive for development)
export const developmentProtectionConfig: Partial<ContentProtectionConfig> = {
  enabled: false, // Disable in development for easier debugging
  developmentMode: true,
  disableDevTools: false,
  devToolsDetection: false,
  showConsoleWarning: false,
  clearConsole: false,
  showWarningMessages: false,
};

// Get the current configuration based on environment
export const getProtectionConfig = (): ContentProtectionConfig => {
  const baseConfig = { ...defaultProtectionConfig };
  
  if (process.env.NODE_ENV === 'production') {
    return { ...baseConfig, ...productionProtectionConfig };
  } else {
    return { ...baseConfig, ...developmentProtectionConfig };
  }
};

// Specific page configurations
export const pageProtectionConfigs = {
  // Home page - full protection
  home: {
    ...defaultProtectionConfig,
    disableTextSelection: true,
    disableRightClick: true,
  },
  
  // About page - moderate protection
  about: {
    ...defaultProtectionConfig,
    disableTextSelection: true,
    disableRightClick: true,
  },
  
  // Services page - full protection
  services: {
    ...defaultProtectionConfig,
    disableTextSelection: true,
    disableRightClick: true,
  },
  
  // Contact page - allow some interaction
  contact: {
    ...defaultProtectionConfig,
    disableTextSelection: false, // Allow text selection for contact info
    allowInputSelection: true,   // Allow form input selection
  },
  
  // Blog page - moderate protection
  blog: {
    ...defaultProtectionConfig,
    disableTextSelection: false, // Allow text selection for reading
    disableRightClick: true,
  },
  
  // Team page - full protection
  team: {
    ...defaultProtectionConfig,
    disableTextSelection: true,
    disableRightClick: true,
    disableImageSaving: true,
  },
};

export default getProtectionConfig;
