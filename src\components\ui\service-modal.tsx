import React from 'react';
import { X, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface ServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  breadcrumbs: string[];
  children: React.ReactNode;
}

export const ServiceModal: React.FC<ServiceModalProps> = ({
  isOpen,
  onClose,
  title,
  breadcrumbs,
  children
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative min-h-screen flex items-start justify-center p-4 sm:p-6 lg:p-8">
        <Card className="relative w-full max-w-6xl mx-auto my-8 shadow-2xl border-0 overflow-hidden">
          {/* Close Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute top-4 right-4 z-10 bg-white/90 hover:bg-white shadow-md"
          >
            <X className="w-4 h-4" />
          </Button>

          {/* Gradient Header */}
          <div className="bg-gradient-to-br from-pentagon-blue via-slate-800 to-pentagon-blue text-white py-5 px-6 lg:px-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              {/* Left: Breadcrumbs */}
              <div className="order-2 lg:order-1">
                <nav className="flex items-center space-x-2 text-sm text-slate-300 mb-4">
                  <Home className="w-4 h-4" />
                  {breadcrumbs.map((crumb, index) => (
                    <React.Fragment key={index}>
                      <ChevronRight className="w-4 h-4" />
                      <span className={index === breadcrumbs.length - 1 ? 'text-white font-medium' : ''}>
                        {crumb}
                      </span>
                    </React.Fragment>
                  ))}
                </nav>
              </div>

              {/* Right: Page Title */}
              <div className="order-1 lg:order-2 text-left lg:text-right">
                <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight">
                  {title}
                </h1>
              </div>
            </div>
          </div>

          {/* Content */}
          <CardContent className="p-0">
            <div className="max-h-[70vh] overflow-y-auto">
              {children}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
