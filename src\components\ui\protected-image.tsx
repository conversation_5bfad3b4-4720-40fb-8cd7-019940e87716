import React, { useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ProtectedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  className?: string;
  showWarning?: boolean;
  warningMessage?: string;
}

const ProtectedImage: React.FC<ProtectedImageProps> = ({
  src,
  alt,
  className,
  showWarning = true,
  warningMessage = "Image is protected and cannot be saved.",
  ...props
}) => {
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const img = imgRef.current;
    if (!img) return;

    const showWarningMessage = () => {
      if (showWarning) {
        alert(warningMessage);
      }
    };

    // Prevent right-click on image
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      showWarningMessage();
      return false;
    };

    // Prevent drag and drop
    const handleDragStart = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      showWarningMessage();
      return false;
    };

    // Prevent selection
    const handleSelectStart = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
      return false;
    };

    // Prevent mouse down for additional protection
    const handleMouseDown = (e: MouseEvent) => {
      if (e.button === 2) { // Right mouse button
        e.preventDefault();
        e.stopPropagation();
        showWarningMessage();
        return false;
      }
    };

    // Add event listeners
    img.addEventListener('contextmenu', handleContextMenu);
    img.addEventListener('dragstart', handleDragStart);
    img.addEventListener('selectstart', handleSelectStart);
    img.addEventListener('mousedown', handleMouseDown);

    // Cleanup
    return () => {
      img.removeEventListener('contextmenu', handleContextMenu);
      img.removeEventListener('dragstart', handleDragStart);
      img.removeEventListener('selectstart', handleSelectStart);
      img.removeEventListener('mousedown', handleMouseDown);
    };
  }, [showWarning, warningMessage]);

  return (
    <img
      ref={imgRef}
      src={src}
      alt={alt}
      className={cn(
        'select-none pointer-events-auto',
        className
      )}
      style={{
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none',
        userSelect: 'none',
        WebkitTouchCallout: 'none',
        WebkitTapHighlightColor: 'transparent',
        pointerEvents: 'auto'
      }}
      draggable={false}
      onDragStart={(e) => e.preventDefault()}
      onContextMenu={(e) => e.preventDefault()}
      {...props}
    />
  );
};

export default ProtectedImage;
