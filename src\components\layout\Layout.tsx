import React from 'react';
import { useLocation } from 'react-router-dom';
import Header from './Header';
import Footer from './Footer';
import useContentProtection from '@/hooks/useContentProtection';
import ProtectedContent from '@/components/ui/protected-content';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({ children, className = '' }) => {
  const location = useLocation();
  const isHomePage = location.pathname === '/';

  // Enable content protection globally
  useContentProtection({
    enabled: true,
    disableRightClick: true,
    disableTextSelection: true,
    disableKeyboardShortcuts: true,
    disableDragAndDrop: true,
    disableDevTools: true,
    showWarningMessages: true,
  });

  return (
    <ProtectedContent className="min-h-screen flex flex-col">
      <Header />
      <main className={`flex-1 ${className} ${isHomePage ? '' : 'pt-24 lg:pt-28'}`}>
        {children}
      </main>
      <Footer />
    </ProtectedContent>
  );
};

export default Layout;
