import React from 'react';
import { Link } from 'react-router-dom';
import { Award, CheckCircle, ArrowRight, FileText, Users, Shield, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Section, SectionHeader } from '@/components/ui/section';

export const EstatePlanningContent = () => {
  const services = [
    {
      title: "Will Drafting & Review",
      description: "Professional will preparation ensuring your wishes are clearly documented and legally valid."
    },
    {
      title: "Trust Establishment",
      description: "Setting up trusts to protect assets and provide for beneficiaries according to your wishes."
    },
    {
      title: "Succession Planning",
      description: "Comprehensive planning for the orderly transfer of assets to the next generation."
    },
    {
      title: "Probate Administration",
      description: "Guidance through the probate process to ensure efficient estate administration."
    },
    {
      title: "Asset Protection",
      description: "Strategies to protect your assets from potential creditors and legal challenges."
    },
    {
      title: "Tax Planning",
      description: "Minimizing tax implications of estate transfers and inheritance arrangements."
    }
  ];

  const process = [
    {
      step: "1",
      title: "Estate Assessment",
      description: "We evaluate your assets, family situation, and estate planning goals."
    },
    {
      step: "2",
      title: "Strategy Development",
      description: "Creating a comprehensive estate plan tailored to your specific needs."
    },
    {
      step: "3",
      title: "Document Preparation",
      description: "Drafting wills, trusts, and other necessary estate planning documents."
    },
    {
      step: "4",
      title: "Implementation & Review",
      description: "Executing your estate plan and providing ongoing reviews and updates."
    }
  ];

  const benefits = [
    {
      icon: Shield,
      title: "Asset Protection",
      description: "Protect your assets and ensure they are distributed according to your wishes."
    },
    {
      icon: Users,
      title: "Family Security",
      description: "Provide financial security and clear guidance for your loved ones."
    },
    {
      icon: Clock,
      title: "Peace of Mind",
      description: "Rest assured knowing your estate is properly planned and documented."
    },
    {
      icon: FileText,
      title: "Legal Compliance",
      description: "Ensure all estate planning documents meet legal requirements and standards."
    }
  ];

  return (
    <>
      {/* Services Overview */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Our Estate Planning Services"
          title="Comprehensive Legacy Protection"
          description="We help you create a comprehensive estate plan that protects your assets and provides for your loved ones."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-pentagon-blue rounded-lg flex items-center justify-center mb-4">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-slate-900 mb-3">{service.title}</h3>
                <p className="text-slate-600 leading-relaxed">{service.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Process Section */}
      <Section className="bg-slate-50">
        <SectionHeader
          subtitle="Our Process"
          title="How We Handle Estate Planning"
          description="Our structured approach ensures comprehensive estate planning that meets your unique needs and goals."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {process.map((item, index) => (
            <Card key={index} className="text-center border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="w-16 h-16 bg-pentagon-blue text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h3 className="text-lg font-bold text-slate-900 mb-3">{item.title}</h3>
                <p className="text-slate-600 leading-relaxed">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Benefits Section */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Why Choose Us"
          title="Benefits of Our Estate Planning Services"
          description="Pentagon Advocates provides comprehensive estate planning services that protect your legacy and provide peace of mind."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => {
            const IconComponent = benefit.icon;
            return (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-pentagon-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-slate-900 mb-3">{benefit.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{benefit.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* Expertise Section */}
      <Section className="bg-slate-50">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <SectionHeader
              subtitle="Our Expertise"
              title="Comprehensive Estate Planning Knowledge"
              description="Our team has extensive experience in all aspects of estate planning, from simple wills to complex trust structures."
              centered={false}
            />
            
            <div className="space-y-4 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Will Preparation</h4>
                  <p className="text-slate-600 text-sm">Expert drafting of wills that clearly express your wishes and meet legal requirements.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Trust Administration</h4>
                  <p className="text-slate-600 text-sm">Comprehensive trust services including establishment, administration, and management.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Probate Guidance</h4>
                  <p className="text-slate-600 text-sm">Expert assistance through the probate process to ensure efficient estate administration.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Tax Optimization</h4>
                  <p className="text-slate-600 text-sm">Strategic planning to minimize tax implications of estate transfers.</p>
                </div>
              </div>
            </div>
            
            <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue">
              <Link to="/contact">Plan Your Estate Today</Link>
            </Button>
          </div>
          
          <div>
            <Card className="bg-white border-0 shadow-xl">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-slate-900 mb-6">Estate Planning Services We Provide</h3>
                <div className="space-y-4">
                  {[
                    "Last will and testament drafting",
                    "Living trust establishment",
                    "Power of attorney documents",
                    "Healthcare directives",
                    "Beneficiary designations",
                    "Estate tax planning",
                    "Succession planning for businesses",
                    "Charitable giving strategies"
                  ].map((service, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <ArrowRight className="w-4 h-4 text-pentagon-blue flex-shrink-0" />
                      <span className="text-slate-700">{service}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>
    </>
  );
};
