import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface ProtectedContentProps {
  children: React.ReactNode;
  className?: string;
  disableRightClick?: boolean;
  disableTextSelection?: boolean;
  disableDragAndDrop?: boolean;
  showWarning?: boolean;
  warningMessage?: string;
}

const ProtectedContent: React.FC<ProtectedContentProps> = ({
  children,
  className,
  disableRightClick = true,
  disableTextSelection = true,
  disableDragAndDrop = true,
  showWarning = true,
  warningMessage = "This content is protected. Unauthorized copying is prohibited."
}) => {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = contentRef.current;
    if (!element) return;

    const showWarningMessage = () => {
      if (showWarning) {
        alert(warningMessage);
      }
    };

    // Handle right-click
    const handleContextMenu = (e: MouseEvent) => {
      if (disableRightClick) {
        e.preventDefault();
        e.stopPropagation();
        showWarningMessage();
        return false;
      }
    };

    // Handle text selection
    const handleSelectStart = (e: Event) => {
      if (disableTextSelection) {
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    };

    // Handle drag start
    const handleDragStart = (e: DragEvent) => {
      if (disableDragAndDrop) {
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    };

    // Handle mouse down for additional protection
    const handleMouseDown = (e: MouseEvent) => {
      if (disableTextSelection && e.detail > 1) {
        e.preventDefault();
        return false;
      }
    };

    // Add event listeners
    element.addEventListener('contextmenu', handleContextMenu);
    element.addEventListener('selectstart', handleSelectStart);
    element.addEventListener('dragstart', handleDragStart);
    element.addEventListener('mousedown', handleMouseDown);

    // Cleanup
    return () => {
      element.removeEventListener('contextmenu', handleContextMenu);
      element.removeEventListener('selectstart', handleSelectStart);
      element.removeEventListener('dragstart', handleDragStart);
      element.removeEventListener('mousedown', handleMouseDown);
    };
  }, [disableRightClick, disableTextSelection, disableDragAndDrop, showWarning, warningMessage]);

  return (
    <div
      ref={contentRef}
      className={cn(
        // Base protection styles
        disableTextSelection && [
          'select-none',
          '[&_*]:select-none',
          '[&_input]:!select-text',
          '[&_textarea]:!select-text',
          '[&_[contenteditable="true"]]:!select-text'
        ],
        disableDragAndDrop && 'pointer-events-auto',
        className
      )}
      style={{
        WebkitUserSelect: disableTextSelection ? 'none' : 'auto',
        MozUserSelect: disableTextSelection ? 'none' : 'auto',
        msUserSelect: disableTextSelection ? 'none' : 'auto',
        userSelect: disableTextSelection ? 'none' : 'auto',
        WebkitTouchCallout: disableTextSelection ? 'none' : 'auto',
        WebkitTapHighlightColor: disableTextSelection ? 'transparent' : 'auto',
      }}
    >
      {children}
    </div>
  );
};

export default ProtectedContent;
