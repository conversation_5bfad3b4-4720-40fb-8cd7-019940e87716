{"name": "pentagon-advocates-backend", "version": "1.0.0", "description": "Backend API for Pentagon Advocates contact form", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "nodemailer": "^6.9.7", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["email", "contact-form", "nodejs", "express"], "author": "Pentagon Advocates", "license": "MIT"}