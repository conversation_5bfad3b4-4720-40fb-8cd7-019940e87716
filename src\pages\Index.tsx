
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Building2, Scale, Award, Heart, ArrowRight, CheckCircle, Star, Calendar, Clock } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Hero } from "@/components/ui/hero";
import { Section, SectionHeader } from "@/components/ui/section";
import { blogPosts } from "@/data/blogPosts";

const Index = () => {
  // Get the 3 most recent blog posts
  const recentPosts = blogPosts
    .sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime())
    .slice(0, 3);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'Land Law': 'text-amber-600',
      'Real Estate': 'text-green-600',
      'Estate Planning': 'text-pentagon-blue',
      'Family Law': 'text-pink-600'
    };
    return colors[category as keyof typeof colors] || 'text-gray-600';
  };

  const services = [
    {
      icon: Scale,
      title: "Land Law & Litigation",
      description: "Expert representation in land disputes, property rights, and ownership matters across Uganda.",
      href: "/services",
      color: "from-pentagon-blue to-pentagon-blue"
    },
    {
      icon: Building2,
      title: "Real Estate Law",
      description: "Comprehensive legal support for property transactions, conveyancing, and real estate investments.",
      href: "/services",
      color: "from-pentagon-blue to-pentagon-blue"
    },
    {
      icon: Award,
      title: "Estate Planning",
      description: "Strategic planning for asset distribution, wills, trusts, and inheritance matters.",
      href: "/services",
      color: "from-pentagon-blue to-pentagon-blue"
    },
    {
      icon: Heart,
      title: "Family Law",
      description: "Compassionate legal support for marriage, divorce, child custody, and domestic relations.",
      href: "/services",
      color: "from-pentagon-blue to-pentagon-blue"
    }
  ];



  const testimonials = [
    {
      name: "Sarah Nakamya",
      role: "Property Developer",
      content: "Pentagon Advocates handled my complex land acquisition with exceptional professionalism. Their expertise in Ugandan property law is unmatched.",
      rating: 5
    },
    {
      name: "James Okello",
      role: "Business Owner",
      content: "The team's dedication to client success is remarkable. They guided me through estate planning with clarity and precision.",
      rating: 5
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <Hero
        title="PENTAGON ADVOCATES"
        description="Leveraging prudent legal services for sustainable business success. Uganda's premier law firm specializing in Land Law, Real Estate, Estate Planning, and Family Law."
        corporateGradient={true}
        firmImage="/firm.webp"
      />

      {/* Services Section */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Our Expertise"
          title="Legal Services We Provide"
          description="Comprehensive legal solutions tailored to meet your unique needs across our core practice areas."
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className={`w-16 h-16 bg-gradient-to-br ${service.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">
                    {service.title}
                  </h3>
                  <p className="text-slate-600 mb-6 leading-relaxed">
                    {service.description}
                  </p>
                  <Link
                    to={service.href}
                    className="inline-flex items-center text-pentagon-blue font-semibold hover:text-pentagon-hover transition-colors duration-200"
                  >
                    Learn More
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </Section>

      {/* About Preview Section */}
      <Section className="bg-slate-50">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <SectionHeader
              subtitle="About Pentagon Advocates"
              title="Founded on Excellence & Integrity"
              description="Established in 2021 by Sengooba Farooq, we combine youthful energy with deep legal expertise to deliver innovative solutions for our clients."
              centered={false}
            />

            <div className="space-y-4 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Client-Centered Approach</h4>
                  <p className="text-slate-600 text-sm">Tailored legal solutions that prioritize your unique needs and objectives.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Innovative Legal Practice</h4>
                  <p className="text-slate-600 text-sm">Bridging traditional legal expertise with modern technology and practices.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-slate-900 mb-1">Community Commitment</h4>
                  <p className="text-slate-600 text-sm">Actively engaged in pro bono work and legal education initiatives.</p>
                </div>
              </div>
            </div>

            <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-white">
              <Link to="/about">Learn More About Us</Link>
            </Button>
          </div>

          <div className="relative">
            <div className="bg-gradient-to-br from-pentagon-blue to-slate-800 rounded-2xl overflow-hidden shadow-2xl">
              {/* Founder Image Section */}
              <div className="relative h-80 bg-gradient-to-br from-slate-700 to-pentagon-blue">
                <img
                  src="/farooqA.jpg"
                  alt="Sengooba Farooq - Founder of Pentagon Advocates"
                  className="w-full h-full object-cover object-center"
                  onError={(e) => {
                    // Fallback to a professional placeholder if image doesn't exist
                    e.currentTarget.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 320'%3E%3Crect width='400' height='320' fill='%2318213E'/%3E%3Ccircle cx='200' cy='140' r='60' fill='%23A5A8B8'/%3E%3Cpath d='M80 280 Q80 220 140 220 h120 Q320 220 320 280' fill='%23A5A8B8'/%3E%3Ctext x='200' y='300' text-anchor='middle' fill='%23A5A8B8' font-size='14' font-family='Arial'%3ESengooba Farooq%3C/text%3E%3C/svg%3E";
                  }}
                />
                {/* Overlay gradient for better text readability */}
                <div className="absolute inset-0 bg-gradient-to-t from-pentagon-blue/80 via-transparent to-transparent"></div>

                {/* Founder info overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">Meet Our Founder</h3>
                  <p className="text-pale-grey text-lg font-semibold">Sengooba Farooq</p>
                  <p className="text-slate-300 text-sm">Founder & Managing Partner</p>
                </div>
              </div>

              {/* Content Section */}
              <div className="p-8 text-white">
                <p className="text-slate-200 leading-relaxed mb-6 italic text-lg">
                  "Our mission is to redefine legal services through reliability, honesty, and cutting-edge solutions.
                  We believe in empowering our clients to navigate legal challenges with confidence."
                </p>
                <div className="flex items-center space-x-4 text-sm text-pale-grey bg-slate-800/30 rounded-lg p-4">
                  <span className="flex items-center">
                    <Award className="w-4 h-4 mr-2" />
                    5+ Years Experience
                  </span>
                  <span>•</span>
                  <span className="flex items-center">
                    <Scale className="w-4 h-4 mr-2" />
                    Land Law Specialist
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Section>

      {/* Testimonials Section */}
      <Section className="bg-white">
        <SectionHeader
          subtitle="Client Testimonials"
          title="What Our Clients Say"
          description="Don't just take our word for it. Here's what our satisfied clients have to say about our legal services."
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-0 shadow-lg">
              <CardContent className="p-8">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-amber-400 fill-current" />
                  ))}
                </div>
                <p className="text-slate-600 mb-6 leading-relaxed italic">
                  "{testimonial.content}"
                </p>
                <div>
                  <h4 className="font-semibold text-slate-900">{testimonial.name}</h4>
                  <p className="text-sm text-slate-500">{testimonial.role}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Blog Preview Section */}
      <Section className="bg-slate-50">
        <SectionHeader
          subtitle="Legal Insights"
          title="Latest from Our Blog"
          description="Stay informed with our latest legal insights, industry updates, and expert commentary on Ugandan law."
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {recentPosts.map((post) => (
            <Card key={post.id} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
              <CardContent className="p-0">
                {/* Image */}
                <div className="h-48 relative overflow-hidden">
                  <img
                    src={post.image}
                    alt={post.title}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-br from-pentagon-blue/20 to-pentagon-blue/30"></div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className={`text-sm font-semibold mb-2 ${getCategoryColor(post.category)}`}>
                    {post.category}
                  </div>
                  <h3 className="text-lg font-bold text-slate-900 mb-3 group-hover:text-pentagon-blue transition-colors line-clamp-2">
                    <Link to={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </h3>
                  <p className="text-slate-600 text-sm mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-xs text-slate-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDate(post.publishDate)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{post.readTime} min read</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button asChild variant="outline" size="lg">
            <Link to="/blog">View All Articles</Link>
          </Button>
        </div>
      </Section>
    </>
  );
};

export default Index;
