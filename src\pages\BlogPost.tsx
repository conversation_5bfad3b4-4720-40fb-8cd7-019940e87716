import React from 'react';
import { use<PERSON>ara<PERSON>, Link, Navigate } from 'react-router-dom';
import { Calendar, Clock, User, ArrowLeft, Share2, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Section } from '@/components/ui/section';
import { getPostBySlug, getRecentPosts } from '@/data/blogPosts';

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>();
  const post = slug ? getPostBySlug(slug) : null;
  const recentPosts = getRecentPosts(3);

  if (!post) {
    return <Navigate to="/blog" replace />;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'Land Law': 'bg-amber-100 text-amber-800',
      'Real Estate': 'bg-green-100 text-green-800',
      'Estate Planning': 'bg-pale-grey/20 text-pentagon-blue',
      'Family Law': 'bg-pink-100 text-pink-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <>
      {/* Article Header */}
      <Section className="bg-gradient-to-br from-slate-50 to-slate-100 pt-8 pb-16">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <div className="mb-8">
            <Button asChild variant="ghost" className="hover:bg-white/50">
              <Link to="/blog" className="flex items-center space-x-2">
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Blog</span>
              </Link>
            </Button>
          </div>

          {/* Category */}
          <div className="mb-4">
            <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getCategoryColor(post.category)}`}>
              {post.category}
            </span>
          </div>

          {/* Title */}
          <h1 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6 leading-tight">
            {post.title}
          </h1>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 text-slate-600 mb-8">
            <div className="flex items-center space-x-2">
              <User className="w-5 h-5" />
              <span>{post.author}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5" />
              <span>{formatDate(post.publishDate)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5" />
              <span>{post.readTime} min read</span>
            </div>
          </div>

          {/* Share Button */}
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" className="flex items-center space-x-2">
              <Share2 className="w-4 h-4" />
              <span>Share Article</span>
            </Button>
          </div>
        </div>
      </Section>

      {/* Article Content */}
      <Section className="bg-white py-12">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <div 
                className="prose prose-lg max-w-none prose-headings:text-slate-900 prose-p:text-slate-700 prose-p:leading-relaxed prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline prose-strong:text-slate-900 prose-ul:text-slate-700 prose-li:text-slate-700"
                dangerouslySetInnerHTML={{ __html: post.content }}
              />

              {/* Tags */}
              <div className="mt-12 pt-8 border-t border-slate-200">
                <div className="flex items-center space-x-2 mb-4">
                  <Tag className="w-5 h-5 text-slate-400" />
                  <span className="text-slate-600 font-semibold">Tags:</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-slate-100 text-slate-700 rounded-full text-sm hover:bg-slate-200 transition-colors cursor-pointer"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* CTA Section */}
              <Card className="mt-12 bg-gradient-to-br from-blue-50 to-blue-100 border-0">
                <CardContent className="p-8 text-center">
                  <h3 className="text-2xl font-bold text-slate-900 mb-4">
                    Need Legal Assistance?
                  </h3>
                  <p className="text-slate-600 mb-6 leading-relaxed">
                    If you have questions about this topic or need professional legal advice,
                    our experienced team at Pentagon Advocates is here to help.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button asChild size="lg" className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue">
                      <Link to="/contact">Schedule Consultation</Link>
                    </Button>
                    <Button asChild variant="outline" size="lg" className="border-pentagon-blue text-pentagon-blue hover:bg-pentagon-blue hover:text-white">
                      <Link to="/services">Our Services</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              {/* Recent Posts */}
              <Card className="sticky top-8">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 mb-6">Recent Articles</h3>
                  <div className="space-y-6">
                    {recentPosts
                      .filter(recentPost => recentPost.id !== post.id)
                      .slice(0, 3)
                      .map((recentPost) => (
                      <div key={recentPost.id} className="group">
                        <div className="mb-2">
                          <span className={`px-2 py-1 rounded text-xs font-semibold ${getCategoryColor(recentPost.category)}`}>
                            {recentPost.category}
                          </span>
                        </div>
                        <h4 className="font-semibold text-slate-900 mb-2 group-hover:text-pentagon-blue transition-colors line-clamp-2">
                          <Link to={`/blog/${recentPost.slug}`}>
                            {recentPost.title}
                          </Link>
                        </h4>
                        <p className="text-sm text-slate-600 mb-2 line-clamp-2">
                          {recentPost.excerpt}
                        </p>
                        <div className="flex items-center text-xs text-slate-500 space-x-3">
                          <span>{formatDate(recentPost.publishDate)}</span>
                          <span>{recentPost.readTime} min read</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-6 pt-6 border-t border-slate-200">
                    <Button asChild variant="outline" className="w-full">
                      <Link to="/blog">View All Articles</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </Section>

      {/* Newsletter Signup */}
      <Section className="bg-slate-50">
        <div className="text-center max-w-2xl mx-auto">
          <h2 className="text-3xl font-bold text-slate-900 mb-4">
            Stay Informed
          </h2>
          <p className="text-slate-600 mb-8 leading-relaxed">
            Subscribe to our newsletter for the latest legal insights and updates from Pentagon Advocates.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Button className="bg-pentagon-blue hover:bg-pentagon-hover hover:text-pentagon-blue px-8">
              Subscribe
            </Button>
          </div>
        </div>
      </Section>
    </>
  );
};

export default BlogPost;
